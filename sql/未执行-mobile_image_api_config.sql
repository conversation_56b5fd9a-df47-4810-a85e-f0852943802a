-- 移动端影像API配置
-- 添加影像API相关配置项到sys_config表

-- 影像API基础URL配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES ('移动端影像API地址', 'mobile.image.apiUrl', 'https://yyx.xinguokeji.cn', 'Y', 'admin', getdate(), 'admin', getdate(), '移动端影像API的基础地址，用于DICOM数据查询等接口');

-- 影像查看器URL配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) 
VALUES ('移动端影像查看器地址', 'mobile.image.viewerUrl', 'https://yyx.xinguokeji.cn/mobile/dicomViewer.html', 'Y', 'admin', getdate(), 'admin', getdate(), '移动端影像查看器的完整地址，用于显示DICOM影像');
