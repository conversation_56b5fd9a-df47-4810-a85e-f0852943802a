-- 立即修复慢SQL问题的紧急脚本
-- 针对当前出现的具体慢查询进行优化

-- =====================================================
-- 第一步：立即添加关键索引
-- =====================================================

-- 1. 为diagnosis_status字段添加索引（解决2896ms慢查询）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'pacs_patient_study' 
    AND index_name = 'idx_pacs_patient_study_diagnosis_status'
);

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_pacs_patient_study_diagnosis_status ON pacs_patient_study(diagnosis_status)',
    'SELECT "diagnosis_status索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 为modality + diagnosis_status组合索引（优化统计查询）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'pacs_patient_study' 
    AND index_name = 'idx_pacs_patient_study_modality_diagnosis'
);

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_pacs_patient_study_modality_diagnosis ON pacs_patient_study(modality, diagnosis_status)',
    'SELECT "modality_diagnosis组合索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为diagnosis表的check_id字段添加索引（解决1818ms慢查询）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = 'diagnosis' 
    AND index_name = 'idx_diagnosis_check_id'
);

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_diagnosis_check_id ON diagnosis(check_id)',
    'SELECT "diagnosis.check_id索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第二步：测试优化效果
-- =====================================================

-- 测试1：诊断状态统计查询性能
SET @start_time = NOW(6);

SELECT COUNT(0) FROM pacs_patient_study p 
WHERE p.modality IN ('MG', 'MR', 'DX', 'CT', 'CR') 
AND p.diagnosis_status = '9';

SET @end_time = NOW(6);

SELECT 
    TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as query_time_ms,
    CASE 
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 100 THEN '优秀(<100ms)'
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 500 THEN '良好(<500ms)'
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 1000 THEN '一般(<1000ms)'
        ELSE '仍需优化(>=1000ms)'
    END as performance_status,
    '诊断状态统计查询优化测试' as test_type;

-- 测试2：诊断记录查询性能
SET @start_time = NOW(6);

SELECT * FROM diagnosis WHERE check_id = '51757' LIMIT 1;

SET @end_time = NOW(6);

SELECT 
    TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as query_time_ms,
    CASE 
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 50 THEN '优秀(<50ms)'
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 100 THEN '良好(<100ms)'
        WHEN TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 < 500 THEN '一般(<500ms)'
        ELSE '仍需优化(>=500ms)'
    END as performance_status,
    '诊断记录查询优化测试' as test_type;

-- =====================================================
-- 第三步：查看数据分布情况
-- =====================================================

-- 查看诊断状态分布
SELECT 
    diagnosis_status,
    COUNT(*) as record_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM pacs_patient_study), 2) as percentage
FROM pacs_patient_study 
WHERE modality IN ('MG', 'MR', 'DX', 'CT', 'CR')
GROUP BY diagnosis_status
ORDER BY record_count DESC;

-- 查看diagnosis表大小
SELECT 
    table_name,
    table_rows,
    ROUND(data_length/1024/1024, 2) as data_size_mb,
    ROUND(index_length/1024/1024, 2) as index_size_mb,
    ROUND((data_length + index_length)/1024/1024, 2) as total_size_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'diagnosis';

-- 查看check_id字段的唯一性
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT check_id) as unique_check_ids,
    CASE 
        WHEN COUNT(*) = COUNT(DISTINCT check_id) THEN '唯一'
        ELSE '有重复'
    END as uniqueness_status
FROM diagnosis;

-- =====================================================
-- 第四步：优化建议
-- =====================================================

SELECT 
    '优化建议' as category,
    CASE 
        WHEN (SELECT COUNT(*) FROM pacs_patient_study WHERE diagnosis_status = '9') > 10000 THEN 
            '建议对diagnosis_status=9的记录进行分页查询，避免一次性加载过多数据'
        ELSE '当前数据量正常'
    END as suggestion
UNION ALL
SELECT 
    '索引使用',
    '新增索引后，请使用EXPLAIN分析查询计划，确保索引被正确使用'
UNION ALL
SELECT 
    '监控建议',
    '持续监控这两个慢查询的执行时间，如仍有问题请考虑进一步优化';

-- =====================================================
-- 第五步：显示完成信息
-- =====================================================

SELECT 
    '立即优化完成' as status,
    NOW() as completion_time,
    '已添加关键索引，预期查询性能提升90%以上' as message;

-- 显示当前索引状态
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index,
    CASE non_unique 
        WHEN 0 THEN '唯一索引'
        ELSE '普通索引'
    END as index_type
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name IN ('pacs_patient_study', 'diagnosis')
AND index_name NOT IN ('PRIMARY')
ORDER BY table_name, index_name, seq_in_index;
