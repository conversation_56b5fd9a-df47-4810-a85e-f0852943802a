# 亲友维护功能说明

## 功能概述

将原有的亲友报告查询逻辑抽取并改造成独立的亲友维护功能，提供完整的亲友关系管理能力。

## 主要改动

### 1. 后端改动

#### 新增文件
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/MobileUserRelationController.java`
  - 亲友维护控制器，提供完整的CRUD操作
  - 包含亲友信息查询、短信验证码发送、亲友关系绑定等功能

#### 修改文件
- `pacs-mobile/src/views/ReportList.vue`
  - 移除了亲友查询相关的代码
  - 添加了"亲友维护"按钮，跳转到亲友维护页面

### 2. 前端改动

#### 新增文件
- `pacs-mobile/src/views/FriendList.vue` - 亲友列表页面
- `pacs-mobile/src/views/FriendDetail.vue` - 亲友详情页面（添加/编辑）
- `pacs-mobile/src/api/friend.js` - 亲友相关API接口

#### 路由配置
- `/friend/list` - 亲友列表
- `/friend/add` - 添加亲友
- `/friend/edit/:id` - 编辑亲友

### 3. 数据库

使用现有的 `mobile_user_relation` 表，包含以下字段：
- `id` - 主键ID
- `user_id` - 当前用户ID
- `user_id_no` - 当前用户身份证号（实际存储手机号）
- `friend_id_no` - 亲友身份证号
- `friend_name` - 亲友姓名
- `friend_phone` - 亲友手机号
- `relation_type` - 关系类型（friend-亲友，family-家属）
- `status` - 状态（0-禁用，1-正常）
- `verified_time` - 验证通过时间
- 标准审计字段（create_time, create_by, update_time, update_by, remark）

## 功能特性

### 1. 亲友信息查询
- 通过身份证号查询患者信息
- 从 `pacs_patient_study` 表中获取关联的手机号
- 返回脱敏手机号用于显示

### 2. 短信验证
- 手动发送短信验证码（不自动发送）
- 验证码有效期5分钟
- 支持重新发送（60秒倒计时）

### 3. 亲友关系管理
- 添加新的亲友关系
- 编辑现有亲友信息
- 删除亲友关系
- 查看亲友列表

### 4. 数据安全
- 身份证号和手机号脱敏显示
- 短信验证码验证
- 用户权限控制

## API接口

### 亲友维护相关接口

1. **GET /mobile/friend/list** - 获取亲友列表
   - 参数：phone（当前用户手机号）
   - 返回：亲友关系列表

2. **GET /mobile/friend/{id}** - 获取亲友详情
   - 参数：id（亲友关系ID）
   - 返回：亲友关系详情

3. **POST /mobile/friend/add** - 添加亲友
   - 参数：亲友信息对象
   - 返回：操作结果

4. **PUT /mobile/friend/edit** - 编辑亲友
   - 参数：亲友信息对象
   - 返回：操作结果

5. **DELETE /mobile/friend/{ids}** - 删除亲友
   - 参数：ids（亲友关系ID数组）
   - 返回：操作结果

6. **POST /mobile/friend/queryByIdNo** - 通过身份证号查询亲友信息
   - 参数：friendIdNo（亲友身份证号）
   - 返回：手机号信息（脱敏）

7. **POST /mobile/friend/sendSmsCode** - 发送短信验证码
   - 参数：friendPhone（亲友手机号）
   - 返回：发送结果

8. **POST /mobile/friend/bindFriend** - 绑定亲友关系
   - 参数：用户信息、亲友信息、验证码
   - 返回：绑定结果

## 使用流程

### 添加亲友流程
1. 用户点击"亲友维护"按钮进入亲友列表页面
2. 点击"添加亲友"按钮进入添加页面
3. 输入亲友身份证号，点击"查询亲友信息"
4. 系统查询并返回亲友手机号（脱敏显示）
5. 用户完善亲友姓名和关系类型
6. 点击"获取验证码"发送短信到亲友手机
7. 输入收到的验证码
8. 点击"添加亲友"完成绑定

### 管理亲友流程
1. 在亲友列表页面查看所有已绑定的亲友
2. 点击亲友条目可以编辑亲友信息
3. 点击删除按钮可以移除亲友关系

## 新增功能

### 1. 保持亲友检查记录查询
- **后端逻辑保持**：ReportList 的后端接口继续支持查询用户及其亲友的检查记录
- **自动关联查询**：当用户通过手机号查询时，系统会自动获取其亲友关系，并查询所有相关的检查记录
- **数据整合**：用户可以在报告列表中看到自己和已绑定亲友的所有检查记录

### 2. 退出功能
- **全局退出按钮**：在所有主要页面的导航栏右侧添加退出按钮
- **确认对话框**：退出前会显示确认对话框，防止误操作
- **完整清理**：退出时会清除用户信息、本地存储和会话存储
- **安全跳转**：退出后自动跳转到登录页面

## 注意事项

1. **用户标识**：系统使用手机号作为用户的主要标识，而不是身份证号
2. **验证码安全**：验证码通过remark字段传递，确保数据安全
3. **数据一致性**：亲友姓名会自动从患者检查记录中获取
4. **权限控制**：所有接口都需要用户登录认证
5. **错误处理**：完善的错误提示和异常处理机制
6. **亲友关系查询**：系统会自动根据用户的亲友关系查询相关检查记录，无需手动切换
7. **数据隔离**：用户只能查看自己和已验证绑定的亲友的检查记录

## 技术栈

- **后端**：Spring Boot + MyBatis + MySQL
- **前端**：Vue 3 + Vant + Vue Router
- **数据库**：MySQL（复用现有表结构）
- **短信服务**：阿里云短信服务
