# SysConfigService.updateConfigByKey 方法实现说明

## 概述

本文档说明了 `SysConfigService.updateConfigByKey` 方法的实现，该方法允许根据配置键名直接更新系统配置值，无需先查询配置ID。

## 实现文件

### 1. 接口定义
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/ISysConfigService.java`

```java
/**
 * 根据键名更新参数配置值
 * 
 * @param configKey 参数键名
 * @param configValue 参数键值
 * @return 结果
 */
public int updateConfigByKey(String configKey, String configValue);
```

### 2. 服务实现
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysConfigServiceImpl.java`

实现逻辑：
1. 参数验证：检查 configKey 是否为空
2. 配置检查：查询配置是否已存在
3. 更新或创建：
   - 如果配置存在，直接更新配置值
   - 如果配置不存在，创建新的配置项
4. 缓存更新：更新 Redis 缓存中的配置值

### 3. 数据访问层
**文件**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/SysConfigMapper.java`

```java
/**
 * 根据键名更新参数配置值
 * 
 * @param configKey 参数键名
 * @param configValue 参数键值
 * @return 结果
 */
public int updateConfigByKey(String configKey, String configValue);
```

### 4. SQL 映射
**文件**: `ruoyi-system/src/main/resources/mapper/system/SysConfigMapper.xml`

```xml
<update id="updateConfigByKey">
    update sys_config 
    set config_value = #{configValue}, 
        update_time = sysdate()
    where config_key = #{configKey}
</update>
```

## 方法特性

### 输入参数
- `configKey`: 配置键名（必填）
- `configValue`: 配置值

### 返回值
- `int`: 影响的数据库记录行数
  - 1: 更新成功
  - 0: 配置不存在且创建失败

### 异常处理
- 当 `configKey` 为空时，抛出 `ServiceException`

### 自动创建功能
如果指定的配置键不存在，方法会自动创建新的配置项：
- `configKey`: 使用传入的键名
- `configValue`: 使用传入的配置值
- `configName`: 使用键名作为配置名称
- `configType`: 设置为 "N"（非系统内置）

### 缓存管理
方法会自动维护 Redis 缓存：
- 更新成功后，自动更新缓存中的配置值
- 缓存键格式：`sys_config:{configKey}`

## 使用示例

```java
@Autowired
private ISysConfigService sysConfigService;

// 更新现有配置
int result = sysConfigService.updateConfigByKey("minio.endpoint", "http://localhost:9000");

// 创建新配置（如果不存在）
int result = sysConfigService.updateConfigByKey("new.config.key", "new.config.value");
```

## 应用场景

该方法主要用于以下场景：
1. **配置管理界面**: 动态更新系统配置
2. **MinIO 配置**: 更新文件存储配置
3. **诊断 PDF 配置**: 更新 PDF 生成相关配置
4. **其他动态配置**: 任何需要运行时更新的系统配置

## 注意事项

1. **线程安全**: 方法实现是线程安全的
2. **事务支持**: 支持数据库事务
3. **缓存一致性**: 自动维护数据库和缓存的一致性
4. **参数验证**: 会验证必要参数的有效性
5. **自动创建**: 不存在的配置会被自动创建

## 相关方法

- `selectConfigByKey(String configKey)`: 根据键名查询配置值
- `updateConfig(SysConfig config)`: 根据配置对象更新配置
- `insertConfig(SysConfig config)`: 插入新配置
