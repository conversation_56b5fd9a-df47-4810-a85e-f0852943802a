# 诊断PDF报告医生信息测试示例

## 测试数据准备

### 1. 创建测试用户

```sql
-- 插入测试医生用户
INSERT INTO sys_user (user_id, user_name, nick_name, email, phonenumber, sex, avatar, status, del_flag, create_by, create_time) VALUES
(100, 'doctor_zhang', '张医生', '<EMAIL>', '13800138001', '1', 'profile/avatar/doctor1.jpg', '0', '0', 'admin', NOW()),
(101, 'doctor_li', '李主任', '<EMAIL>', '13800138002', '1', 'profile/avatar/doctor2.jpg', '0', '0', 'admin', NOW());
```

### 2. 创建测试诊断记录

```sql
-- 插入测试诊断记录
INSERT INTO diagnosis (id, diagnose, doctor, audit_by, status, study_id, patient_id, create_time) VALUES
(1001, '右肺上叶结节，建议进一步检查', 'doctor_zhang', 'doctor_li', '2', '12345', 'P001', NOW());
```

## 测试方法

### 1. 单元测试

创建测试类验证 `prepareReportData` 方法：

```java
@Test
public void testPrepareReportDataWithDoctorInfo() {
    // 准备测试数据
    Diagnosis diagnosis = new Diagnosis();
    diagnosis.setId(1001L);
    diagnosis.setDoctor("doctor_zhang");
    diagnosis.setAuditBy("doctor_li");
    diagnosis.setStudyId("12345");
    
    // 调用方法
    Map<String, Object> reportData = diagnosisPdfGeneratorService.prepareReportDataPublic(diagnosis);
    
    // 验证报告医生信息
    Map<String, Object> reportDoctor = (Map<String, Object>) reportData.get("reportDoctor");
    assertNotNull(reportDoctor);
    assertEquals("张医生", reportDoctor.get("name"));
    assertTrue(((String) reportDoctor.get("signatureUrl")).contains("doctor1.jpg"));
    
    // 验证审核医生信息
    Map<String, Object> auditDoctor = (Map<String, Object>) reportData.get("auditDoctor");
    assertNotNull(auditDoctor);
    assertEquals("李主任", auditDoctor.get("name"));
    assertTrue(((String) auditDoctor.get("signatureUrl")).contains("doctor2.jpg"));
}
```

### 2. 集成测试

通过API接口测试完整流程：

```bash
# 生成指定诊断的PDF报告
curl -X POST "http://localhost:8080/diagnosis/generatePdf/1001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 预期结果

### 1. 报告数据结构

```json
{
  "hospitalName": "鄂托克旗人民医院",
  "reportTitle": "CT检查报告",
  "diagnosis": { /* 诊断对象 */ },
  "study": { /* 检查信息 */ },
  "qrCode": "https://example.com/?query=P001",
  
  "reportDoctor": {
    "name": "张医生",
    "signatureUrl": "http://localhost:9000/profile/avatar/doctor1.jpg"
  },
  
  "auditDoctor": {
    "name": "李主任",
    "signatureUrl": "http://localhost:9000/profile/avatar/doctor2.jpg"
  },
  
  "doctorSignature": "http://localhost:9000/profile/avatar/current_user.jpg"
}
```

### 2. 日志输出

```
INFO  - 获取到医生信息: 姓名=张医生, 签名URL=http://localhost:9000/profile/avatar/doctor1.jpg
INFO  - 获取到医生信息: 姓名=李主任, 签名URL=http://localhost:9000/profile/avatar/doctor2.jpg
INFO  - 报告数据和模板准备完成，开始生成PDF...
```

## 异常情况测试

### 1. 医生用户不存在

```sql
-- 创建医生用户名不存在的诊断记录
INSERT INTO diagnosis (id, diagnose, doctor, audit_by, status, study_id, patient_id) VALUES
(1002, '测试诊断', 'nonexistent_doctor', 'doctor_li', '2', '12346', 'P002');
```

**预期结果**：
```json
{
  "reportDoctor": {
    "name": "nonexistent_doctor",
    "signatureUrl": ""  // 或默认签名URL
  }
}
```

### 2. 医生未设置头像

```sql
-- 创建未设置头像的医生用户
INSERT INTO sys_user (user_id, user_name, nick_name, avatar, status, del_flag) VALUES
(102, 'doctor_wang', '王医生', NULL, '0', '0');

INSERT INTO diagnosis (id, diagnose, doctor, status, study_id, patient_id) VALUES
(1003, '测试诊断', 'doctor_wang', '2', '12347', 'P003');
```

**预期结果**：
```json
{
  "reportDoctor": {
    "name": "王医生",
    "signatureUrl": ""  // 或默认签名URL
  }
}
```

## 性能测试

### 1. 批量生成测试

```sql
-- 创建多条诊断记录
INSERT INTO diagnosis (diagnose, doctor, audit_by, status, study_id, patient_id, create_time) 
SELECT 
  CONCAT('诊断内容 ', n), 
  'doctor_zhang', 
  'doctor_li', 
  '2', 
  CONCAT('STUDY', n), 
  CONCAT('P', LPAD(n, 3, '0')), 
  NOW()
FROM (
  SELECT ROW_NUMBER() OVER () as n 
  FROM information_schema.columns 
  LIMIT 100
) t;
```

**监控指标**：
- 数据库查询次数
- 内存使用情况
- PDF生成时间
- 用户信息查询耗时

### 2. 并发测试

使用JMeter或类似工具模拟并发PDF生成请求，验证：
- 用户信息查询的线程安全性
- 数据库连接池使用情况
- 系统响应时间

## 配置验证

### 1. 文件服务器配置

```sql
-- 验证文件服务器配置
SELECT config_key, config_value FROM sys_config WHERE config_key = 'file.server.url';

-- 如果不存在，插入配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, remark) 
VALUES ('文件服务器地址', 'file.server.url', 'http://localhost:9000', 'N', '用于生成完整的文件访问URL');
```

### 2. 默认签名配置

```sql
-- 验证默认签名配置
SELECT config_key, config_value FROM sys_config WHERE config_key = 'diagnosis.pdf.defaultSignature';

-- 如果不存在，插入配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, remark) 
VALUES ('默认医生签名', 'diagnosis.pdf.defaultSignature', 'http://localhost:9000/static/default-signature.png', 'N', '默认医生签名图片');
```

## 故障排除

### 1. 常见问题

**问题1**：签名图片URL不正确
- 检查 `file.server.url` 配置
- 验证用户头像路径格式
- 确认MinIO服务是否正常

**问题2**：医生姓名显示为用户名而非昵称
- 检查用户表中 `nick_name` 字段是否有值
- 验证用户信息查询逻辑

**问题3**：PDF生成失败
- 查看详细错误日志
- 检查模板是否支持新的数据字段
- 验证JavaScript模板引擎兼容性

### 2. 调试方法

```java
// 在DiagnosisPdfGeneratorService中添加调试日志
log.debug("准备报告数据，诊断ID: {}, 报告医生: {}, 审核医生: {}", 
    diagnosis.getId(), diagnosis.getDoctor(), diagnosis.getAuditBy());

// 输出完整的报告数据
log.debug("报告数据: {}", objectMapper.writeValueAsString(reportData));
```

## 验收标准

1. ✅ 报告数据中包含 `reportDoctor` 和 `auditDoctor` 对象
2. ✅ 医生姓名优先使用昵称，其次使用用户名
3. ✅ 签名图片URL为完整的可访问地址
4. ✅ 用户不存在时使用默认值，不影响PDF生成
5. ✅ 保持向后兼容性，现有模板仍能正常工作
6. ✅ 异常情况下有适当的日志记录
7. ✅ 性能影响在可接受范围内
