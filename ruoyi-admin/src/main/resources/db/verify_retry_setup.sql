-- 验证DICOM重试机制设置的SQL脚本
-- 用于检查重试相关的配置和数据是否正确

-- 1. 检查dicom_apply表结构是否包含重试相关字段
DESCRIBE dicom_apply;

-- 2. 检查是否有重试相关的定时任务
SELECT
    job_id,
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    CASE status
        WHEN '0' THEN '启用'
        WHEN '1' THEN '禁用'
        ELSE '未知'
    END as status_desc,
    create_time,
    remark
FROM sys_job
WHERE invoke_target LIKE '%retry%' OR job_name LIKE '%重试%'
ORDER BY job_id;

-- 2.1 检查pacs_patient_study表结构是否包含重试相关字段
DESCRIBE pacs_patient_study;

-- 3. 检查同步失败的申请数量
SELECT
    status,
    COUNT(*) as count,
    AVG(sync_retry_count) as avg_retry_count,
    MAX(sync_retry_count) as max_retry_count
FROM dicom_apply
WHERE status IN ('同步失败', '重试处理中')
GROUP BY status;

-- 3.1 检查PACS患者检查记录同步失败的数量
SELECT
    dicom_sync_flag,
    COUNT(*) as count,
    AVG(sync_retry_count) as avg_retry_count,
    MAX(sync_retry_count) as max_retry_count
FROM pacs_patient_study
WHERE dicom_sync_flag = '0' AND sync_error_code IS NOT NULL
GROUP BY dicom_sync_flag;

-- 4. 检查各种错误代码的分布
SELECT 
    sync_error_code,
    COUNT(*) as count,
    AVG(sync_retry_count) as avg_retry_count
FROM dicom_apply 
WHERE status = '同步失败' AND sync_error_code IS NOT NULL
GROUP BY sync_error_code
ORDER BY count DESC;

-- 5. 检查需要重试的申请（模拟重试查询逻辑）
SELECT
    id,
    patient_name,
    status,
    sync_error_code,
    sync_retry_count,
    last_sync_attempt_time,
    TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) as minutes_since_last_attempt
FROM dicom_apply
WHERE status = '同步失败'
AND (sync_retry_count IS NULL OR sync_retry_count < 3)
AND (last_sync_attempt_time IS NULL OR
     TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) >= 1)
AND sync_error_code IN ('CONNECTIVITY_ERROR', 'TIMEOUT', 'PARTIAL_FAILURE', 'SYSTEM_ERROR', 'INTERRUPTED', 'PERMISSION_DENIED')
ORDER BY last_sync_attempt_time ASC, create_time ASC
LIMIT 10;

-- 5.1 检查需要重试的PACS患者检查记录（模拟重试查询逻辑）
SELECT
    id,
    patient_name,
    original_patient_id,
    dicom_sync_flag,
    sync_error_code,
    sync_retry_count,
    last_sync_attempt_time,
    TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) as minutes_since_last_attempt
FROM pacs_patient_study
WHERE dicom_sync_flag = '0'
AND (sync_retry_count IS NULL OR sync_retry_count < 3)
AND (last_sync_attempt_time IS NULL OR
     TIMESTAMPDIFF(MINUTE, last_sync_attempt_time, NOW()) >= 1)
AND sync_error_code IN ('CONNECTIVITY_ERROR', 'TIMEOUT', 'PARTIAL_FAILURE', 'SYSTEM_ERROR', 'INTERRUPTED', 'PERMISSION_DENIED')
ORDER BY last_sync_attempt_time ASC, check_finish_time ASC
LIMIT 10;

-- 6. 统计各状态的申请数量
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM dicom_apply), 2) as percentage
FROM dicom_apply 
GROUP BY status
ORDER BY count DESC;

-- 7. 检查最近的同步活动
SELECT 
    DATE(create_time) as date,
    status,
    COUNT(*) as count
FROM dicom_apply 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(create_time), status
ORDER BY date DESC, status;

-- 8. 显示重试机制相关的配置建议
SELECT 
    '重试配置检查' as check_item,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_job WHERE invoke_target LIKE '%retry%') > 0 
        THEN '✓ 重试定时任务已配置'
        ELSE '✗ 缺少重试定时任务'
    END as status;

SELECT 
    '失败申请检查' as check_item,
    CASE 
        WHEN (SELECT COUNT(*) FROM dicom_apply WHERE status = '同步失败') > 0 
        THEN CONCAT('发现 ', (SELECT COUNT(*) FROM dicom_apply WHERE status = '同步失败'), ' 条失败申请')
        ELSE '✓ 暂无失败申请'
    END as status;
