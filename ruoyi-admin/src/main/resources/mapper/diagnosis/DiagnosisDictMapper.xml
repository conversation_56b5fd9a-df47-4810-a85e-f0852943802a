<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.diagnosis.mapper.DiagnosisDictMapper">
    
    <resultMap type="DiagnosisDict" id="DiagnosisDictResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="categoryId"    column="category_id"    />
        <result property="content"    column="content"    />
        <result property="seq"    column="seq"    />
        <result property="enableFlag"    column="enable_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDiagnosisDictVo">
        select id, title, category_id, content, seq, enable_flag, create_time, create_by, update_time, update_by from diagnosis_dict
    </sql>

    <select id="selectDiagnosisDictList" parameterType="DiagnosisDict" resultMap="DiagnosisDictResult">
        <include refid="selectDiagnosisDictVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="seq != null "> and seq = #{seq}</if>
            <if test="enableFlag != null  and enableFlag != ''"> and enable_flag = #{enableFlag}</if>
        </where>
    </select>
    
    <select id="selectDiagnosisDictById" parameterType="Long" resultMap="DiagnosisDictResult">
        <include refid="selectDiagnosisDictVo"/>
        where id = #{id}
    </select>

    <insert id="insertDiagnosisDict" parameterType="DiagnosisDict" useGeneratedKeys="true" keyProperty="id">
        insert into diagnosis_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="content != null">content,</if>
            <if test="seq != null">seq,</if>
            <if test="enableFlag != null">enable_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="content != null">#{content},</if>
            <if test="seq != null">#{seq},</if>
            <if test="enableFlag != null">#{enableFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateDiagnosisDict" parameterType="DiagnosisDict">
        update diagnosis_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="seq != null">seq = #{seq},</if>
            <if test="enableFlag != null">enable_flag = #{enableFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiagnosisDictById" parameterType="Long">
        delete from diagnosis_dict where id = #{id}
    </delete>

    <delete id="deleteDiagnosisDictByIds" parameterType="String">
        delete from diagnosis_dict where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>