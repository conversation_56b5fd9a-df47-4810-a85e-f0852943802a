package com.ruoyi.dicomSync.strategy.impl;

import com.ruoyi.dicomSync.config.DicomHybridSyncConfig;
import com.ruoyi.dicomSync.domain.DicomSyncResult;
import com.ruoyi.dicomSync.enums.DicomSyncMode;
import com.ruoyi.dicomSync.strategy.DicomSyncStrategy;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 数据库直连同步策略实现
 * 直接查询源PACS系统数据库获取DICOM文件信息
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Component
@Slf4j
public class DatabaseDirectSyncStrategy implements DicomSyncStrategy {
    
    @Autowired
    private DicomHybridSyncConfig hybridConfig;
    
    private ExecutorService executorService;
    private DataSource externalDataSource;
    
    @PostConstruct
    public void init() {
        log.info("初始化数据库直连同步策略");
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "database-strategy-worker");
            t.setDaemon(true);
            return t;
        });
        
        // 初始化外部数据源（如果配置了的话）
        initializeExternalDataSource();
    }
    
    @PreDestroy
    public void cleanup() {
        log.info("清理数据库直连同步策略资源");
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 清理数据源连接
        if (externalDataSource != null) {
            // 这里可以添加数据源清理逻辑
        }
    }
    
    @Override
    public DicomSyncMode getSupportedMode() {
        return DicomSyncMode.DATABASE_DIRECT;
    }
    
    @Override
    public boolean isAvailable(String patientId) {
        try {
            DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
            
            // 检查是否启用
            if (!dbConfig.isEnabled()) {
                return false;
            }
            
            // 检查数据库配置
            if (!StringUtils.hasText(dbConfig.getJdbcUrl()) || 
                !StringUtils.hasText(dbConfig.getUsername())) {
                return false;
            }
            
            // 测试数据库连接
            return testConnection();
        } catch (Exception e) {
            log.debug("数据库直连策略可用性检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getUnavailableReason(String patientId) {
        try {
            DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
            
            if (!dbConfig.isEnabled()) {
                return "数据库直连模式未启用";
            }
            
            if (!StringUtils.hasText(dbConfig.getJdbcUrl())) {
                return "数据库连接URL未配置";
            }
            
            if (!StringUtils.hasText(dbConfig.getUsername())) {
                return "数据库用户名未配置";
            }
            
            if (!testConnection()) {
                return "无法连接到数据库: " + dbConfig.getJdbcUrl();
            }
            
            return "策略可用";
        } catch (Exception e) {
            return "检查可用性时发生异常: " + e.getMessage();
        }
    }
    
    @Override
    public DicomSyncResult syncDicomData(String patientId) {
        log.info("使用数据库直连策略同步患者数据: {}", patientId);
        
        Date startTime = new Date();
        DicomSyncResult.DicomSyncResultBuilder resultBuilder = DicomSyncResult.builder()
                .patientId(patientId)
                .startTime(startTime);
        
        try {
            // 查询患者的研究列表
            List<String> studyUIDs = queryStudyUIDs(patientId);
            
            if (studyUIDs.isEmpty()) {
                return resultBuilder
                        .success(false)
                        .errorCode("NO_STUDIES_FOUND")
                        .errorMessage("未找到患者的研究数据")
                        .endTime(new Date())
                        .build();
            }
            
            int totalStudies = studyUIDs.size();
            int successfulStudies = 0;
            int failedStudies = 0;
            List<String> successfulStudyUIDs = new ArrayList<>();
            List<String> failedStudyUIDs = new ArrayList<>();
            List<String> detailErrors = new ArrayList<>();
            
            // 逐个研究进行同步
            for (String studyUID : studyUIDs) {
                try {
                    boolean studySuccess = syncStudyFromDatabase(studyUID);
                    if (studySuccess) {
                        successfulStudies++;
                        successfulStudyUIDs.add(studyUID);
                    } else {
                        failedStudies++;
                        failedStudyUIDs.add(studyUID);
                        detailErrors.add("研究 " + studyUID + " 同步失败");
                    }
                } catch (Exception e) {
                    failedStudies++;
                    failedStudyUIDs.add(studyUID);
                    detailErrors.add("研究 " + studyUID + " 同步异常: " + e.getMessage());
                    log.error("同步研究 {} 时发生异常", studyUID, e);
                }
            }
            
            boolean overallSuccess = successfulStudies > 0 && failedStudies <= 5; // 允许最多5个失败
            
            return resultBuilder
                    .success(overallSuccess)
                    .totalStudies(totalStudies)
                    .successfulStudies(successfulStudies)
                    .failedStudies(failedStudies)
                    .successfulStudyUIDs(successfulStudyUIDs)
                    .failedStudyUIDs(failedStudyUIDs)
                    .detailErrors(detailErrors)
                    .errorCode(overallSuccess ? "SUCCESS" : "PARTIAL_FAILURE")
                    .errorMessage(overallSuccess ? "同步成功" : "部分研究同步失败")
                    .endTime(new Date())
                    .build();
                    
        } catch (Exception e) {
            log.error("数据库直连策略同步失败: {}", e.getMessage(), e);
            return resultBuilder
                    .success(false)
                    .errorCode("DATABASE_SYNC_ERROR")
                    .errorMessage("数据库直连同步失败: " + e.getMessage())
                    .endTime(new Date())
                    .build();
        }
    }
    
    @Override
    public List<DicomSyncResult> batchSyncDicomData(List<String> patientIds) {
        List<DicomSyncResult> results = new ArrayList<>();
        
        for (String patientId : patientIds) {
            try {
                DicomSyncResult result = syncDicomData(patientId);
                results.add(result);
            } catch (Exception e) {
                log.error("批量同步患者 {} 失败: {}", patientId, e.getMessage(), e);
                results.add(DicomSyncResult.builder()
                        .success(false)
                        .patientId(patientId)
                        .errorCode("BATCH_SYNC_ERROR")
                        .errorMessage("批量同步失败: " + e.getMessage())
                        .build());
            }
        }
        
        return results;
    }
    
    @Override
    public List<String> queryStudyUIDs(String patientId) {
        log.info("使用数据库直连策略查询患者研究列表: {}", patientId);
        
        List<String> studyUIDs = new ArrayList<>();
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(dbConfig.getStudyQuery())) {
            
            stmt.setString(1, patientId);
            stmt.setQueryTimeout(dbConfig.getQueryTimeoutSeconds());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String studyUID = rs.getString("study_uid");
                    if (StringUtils.hasText(studyUID)) {
                        studyUIDs.add(studyUID);
                    }
                }
            }
            
            log.info("查询到患者 {} 的 {} 个研究", patientId, studyUIDs.size());
            
        } catch (SQLException e) {
            log.error("查询患者研究列表失败: {}", e.getMessage(), e);
        }
        
        return studyUIDs;
    }
    
    @Override
    public boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (Exception e) {
            log.debug("数据库连接测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getConfigurationInfo() {
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        return String.format("数据库直连配置 - URL: %s, 用户: %s, 连接池大小: %d", 
            maskUrl(dbConfig.getJdbcUrl()), dbConfig.getUsername(), dbConfig.getMaxPoolSize());
    }
    
    @Override
    public String getPerformanceMetrics() {
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        return String.format("数据库直连性能 - 查询超时: %d秒, 连接池: %d", 
            dbConfig.getQueryTimeoutSeconds(), dbConfig.getMaxPoolSize());
    }
    
    @Override
    public String getEstimatedSpeed() {
        return "medium"; // 数据库查询速度中等
    }
    
    @Override
    public String getReliabilityLevel() {
        return "medium"; // 数据库连接可靠性中等
    }
    
    /**
     * 初始化外部数据源
     */
    private void initializeExternalDataSource() {
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        
        if (!dbConfig.isEnabled() || !StringUtils.hasText(dbConfig.getJdbcUrl())) {
            log.debug("数据库直连未配置，跳过数据源初始化");
            return;
        }
        
        try {
            // 这里可以初始化连接池数据源
            // 为了简化，暂时使用DriverManager
            Class.forName(dbConfig.getDriverClassName());
            log.info("数据库驱动加载成功: {}", dbConfig.getDriverClassName());
        } catch (ClassNotFoundException e) {
            log.error("数据库驱动加载失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取数据库连接
     */
    private Connection getConnection() throws SQLException {
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        
        return DriverManager.getConnection(
            dbConfig.getJdbcUrl(),
            dbConfig.getUsername(),
            dbConfig.getPassword()
        );
    }
    
    /**
     * 从数据库同步研究数据
     */
    private boolean syncStudyFromDatabase(String studyUID) {
        log.info("开始从数据库同步研究: {}", studyUID);
        
        try {
            // 查询研究的文件路径
            List<String> filePaths = queryStudyFilePaths(studyUID);
            
            if (filePaths.isEmpty()) {
                log.warn("研究 {} 没有找到文件路径", studyUID);
                return false;
            }
            
            // 处理文件路径，这里可以实现实际的文件传输逻辑
            int successCount = 0;
            for (String filePath : filePaths) {
                try {
                    if (processStudyFile(filePath)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理文件失败: {}, 错误: {}", filePath, e.getMessage());
                }
            }
            
            // 如果超过80%的文件处理成功，认为研究同步成功
            boolean success = (double) successCount / filePaths.size() >= 0.8;
            log.info("研究 {} 同步完成，成功: {}/{}, 结果: {}", 
                studyUID, successCount, filePaths.size(), success ? "成功" : "失败");
            
            return success;
            
        } catch (Exception e) {
            log.error("从数据库同步研究失败: {}, 错误: {}", studyUID, e.getMessage());
            return false;
        }
    }
    
    /**
     * 查询研究的文件路径
     */
    private List<String> queryStudyFilePaths(String studyUID) throws SQLException {
        List<String> filePaths = new ArrayList<>();
        DicomHybridSyncConfig.DatabaseConfig dbConfig = hybridConfig.getDatabase();
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(dbConfig.getFilePathQuery())) {
            
            stmt.setString(1, studyUID);
            stmt.setQueryTimeout(dbConfig.getQueryTimeoutSeconds());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String filePath = rs.getString("file_path");
                    if (StringUtils.hasText(filePath)) {
                        filePaths.add(filePath);
                    }
                }
            }
        }
        
        return filePaths;
    }
    
    /**
     * 处理研究文件
     */
    private boolean processStudyFile(String filePath) {
        log.debug("处理研究文件: {}", filePath);
        
        // TODO: 实现实际的文件处理逻辑
        // 1. 读取文件
        // 2. 验证DICOM格式
        // 3. 传输到dcm4chee
        // 4. 更新状态
        
        return true; // 临时返回成功
    }
    
    /**
     * 屏蔽URL中的敏感信息
     */
    private String maskUrl(String url) {
        if (url == null) {
            return "未配置";
        }
        
        // 简单的URL屏蔽，隐藏密码等敏感信息
        return url.replaceAll("password=[^&]*", "password=***");
    }
}
