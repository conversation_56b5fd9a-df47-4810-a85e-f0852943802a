package com.ruoyi.dicomSync.strategy.impl;

import com.ruoyi.dicomSync.config.DicomHybridSyncConfig;
import com.ruoyi.dicomSync.config.DicomSyncConfig;
import com.ruoyi.dicomSync.domain.DicomSyncResult;
import com.ruoyi.dicomSync.enums.DicomSyncMode;
import com.ruoyi.dicomSync.service.DicomIntegrityCheckService;
import com.ruoyi.dicomSync.strategy.DicomSyncStrategy;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.dcm4che3.data.Attributes;
import org.dcm4che3.data.Tag;
import org.dcm4che3.io.DicomInputStream;
import org.dcm4che3.net.*;
import org.dcm4che3.net.pdu.AAssociateRQ;
import org.dcm4che3.net.pdu.PresentationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件系统同步策略实现
 * 直接扫描和读取源PACS系统的DICOM文件存储目录
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Component
@Slf4j
public class FileSystemSyncStrategy implements DicomSyncStrategy {
    
    @Autowired
    private DicomHybridSyncConfig hybridConfig;

    @Autowired
    private DicomSyncConfig config;

    @Autowired
    private DicomIntegrityCheckService integrityCheckService;
    
    private ExecutorService dicomExecutor;
    private ScheduledExecutorService dicomScheduledExecutor;
    
    // 缓存扫描结果，避免重复扫描
    private Map<String, List<DicomFileInfo>> scanCache = new HashMap<>();
    private long lastScanTime = 0;
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟缓存
    
    @PostConstruct
    public void init() {
        log.info("初始化文件系统同步策略");
        this.dicomExecutor = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "filesystem-strategy-worker");
            t.setDaemon(true);
            return t;
        });
        this.dicomScheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "filesystem-strategy-scheduler");
            t.setDaemon(true);
            return t;
        });
    }
    
    @PreDestroy
    public void cleanup() {
        log.info("清理文件系统同步策略资源");
        if (dicomExecutor != null && !dicomExecutor.isShutdown()) {
            dicomExecutor.shutdown();
            try {
                if (!dicomExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    dicomExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                dicomExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (dicomScheduledExecutor != null && !dicomScheduledExecutor.isShutdown()) {
            dicomScheduledExecutor.shutdown();
        }
        
        // 清理缓存
        scanCache.clear();
    }
    
    @Override
    public DicomSyncMode getSupportedMode() {
        return DicomSyncMode.FILE_SYSTEM;
    }
    
    @Override
    public boolean isAvailable(String patientId) {
        try {
            DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
            
            // 检查是否启用
            if (!fsConfig.isEnabled()) {
                return false;
            }
            
            // 检查源路径配置
            if (!StringUtils.hasText(fsConfig.getSourcePath())) {
                return false;
            }
            
            // 检查路径是否存在且可读
            Path sourcePath = Paths.get(fsConfig.getSourcePath());
            if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath) || !Files.isReadable(sourcePath)) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.debug("文件系统策略可用性检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getUnavailableReason(String patientId) {
        try {
            DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
            
            if (!fsConfig.isEnabled()) {
                return "文件系统模式未启用";
            }
            
            if (!StringUtils.hasText(fsConfig.getSourcePath())) {
                return "源PACS存储路径未配置";
            }
            
            Path sourcePath = Paths.get(fsConfig.getSourcePath());
            if (!Files.exists(sourcePath)) {
                return "源PACS存储路径不存在: " + fsConfig.getSourcePath();
            }
            
            if (!Files.isDirectory(sourcePath)) {
                return "源PACS存储路径不是目录: " + fsConfig.getSourcePath();
            }
            
            if (!Files.isReadable(sourcePath)) {
                return "源PACS存储路径不可读: " + fsConfig.getSourcePath();
            }
            
            return "策略可用";
        } catch (Exception e) {
            return "检查可用性时发生异常: " + e.getMessage();
        }
    }
    
    @Override
    public DicomSyncResult syncDicomData(String patientId) {
        log.info("使用文件系统策略同步患者数据: {}", patientId);
        
        Date startTime = new Date();
        DicomSyncResult.DicomSyncResultBuilder resultBuilder = DicomSyncResult.builder()
                .patientId(patientId)
                .startTime(startTime);
        
        try {
            // 查询患者的DICOM文件
            List<DicomFileInfo> dicomFiles = findDicomFiles(patientId);
            
            if (dicomFiles.isEmpty()) {
                return resultBuilder
                        .success(false)
                        .errorCode("NO_FILES_FOUND")
                        .errorMessage("未找到患者的DICOM文件")
                        .endTime(new Date())
                        .build();
            }
            
            // 按研究分组
            Map<String, List<DicomFileInfo>> studyGroups = dicomFiles.stream()
                    .collect(Collectors.groupingBy(DicomFileInfo::getStudyUID));
            
            int totalStudies = studyGroups.size();
            int successfulStudies = 0;
            int failedStudies = 0;
            List<String> successfulStudyUIDs = new ArrayList<>();
            List<String> failedStudyUIDs = new ArrayList<>();
            List<String> detailErrors = new ArrayList<>();
            
            // 逐个研究进行同步
            for (Map.Entry<String, List<DicomFileInfo>> entry : studyGroups.entrySet()) {
                String studyUID = entry.getKey();
                List<DicomFileInfo> studyFiles = entry.getValue();
                
                try {
                    boolean studySuccess = syncStudyFiles(studyUID, studyFiles);
                    if (studySuccess) {
                        successfulStudies++;
                        successfulStudyUIDs.add(studyUID);
                    } else {
                        failedStudies++;
                        failedStudyUIDs.add(studyUID);
                        detailErrors.add("研究 " + studyUID + " 同步失败");
                    }
                } catch (Exception e) {
                    failedStudies++;
                    failedStudyUIDs.add(studyUID);
                    detailErrors.add("研究 " + studyUID + " 同步异常: " + e.getMessage());
                    log.error("同步研究 {} 时发生异常", studyUID, e);
                }
            }
            
            boolean overallSuccess = successfulStudies > 0 && failedStudies <= 5; // 允许最多5个失败
            
            return resultBuilder
                    .success(overallSuccess)
                    .totalStudies(totalStudies)
                    .successfulStudies(successfulStudies)
                    .failedStudies(failedStudies)
                    .successfulStudyUIDs(successfulStudyUIDs)
                    .failedStudyUIDs(failedStudyUIDs)
                    .detailErrors(detailErrors)
                    .errorCode(overallSuccess ? "SUCCESS" : "PARTIAL_FAILURE")
                    .errorMessage(overallSuccess ? "同步成功" : "部分研究同步失败")
                    .endTime(new Date())
                    .build();
                    
        } catch (Exception e) {
            log.error("文件系统策略同步失败: {}", e.getMessage(), e);
            return resultBuilder
                    .success(false)
                    .errorCode("FILESYSTEM_SYNC_ERROR")
                    .errorMessage("文件系统同步失败: " + e.getMessage())
                    .endTime(new Date())
                    .build();
        }
    }
    
    @Override
    public List<DicomSyncResult> batchSyncDicomData(List<String> patientIds) {
        List<DicomSyncResult> results = new ArrayList<>();
        
        for (String patientId : patientIds) {
            try {
                DicomSyncResult result = syncDicomData(patientId);
                results.add(result);
            } catch (Exception e) {
                log.error("批量同步患者 {} 失败: {}", patientId, e.getMessage(), e);
                results.add(DicomSyncResult.builder()
                        .success(false)
                        .patientId(patientId)
                        .errorCode("BATCH_SYNC_ERROR")
                        .errorMessage("批量同步失败: " + e.getMessage())
                        .build());
            }
        }
        
        return results;
    }
    
    @Override
    public List<String> queryStudyUIDs(String patientId) {
        log.info("使用文件系统策略查询患者研究列表: {}", patientId);
        
        try {
            List<DicomFileInfo> dicomFiles = findDicomFiles(patientId);
            return dicomFiles.stream()
                    .map(DicomFileInfo::getStudyUID)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询患者研究列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean testConnection() {
        try {
            DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
            Path sourcePath = Paths.get(fsConfig.getSourcePath());
            
            // 测试路径访问
            if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath) || !Files.isReadable(sourcePath)) {
                return false;
            }
            
            // 尝试列出目录内容
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(sourcePath)) {
                stream.iterator().hasNext(); // 只是测试是否可以访问
                return true;
            }
        } catch (Exception e) {
            log.debug("文件系统连接测试失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public String getConfigurationInfo() {
        DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
        return String.format("文件系统配置 - 源路径: %s, 扫描模式: %s, 文件模式: %s", 
            fsConfig.getSourcePath(), fsConfig.getScanMode(), fsConfig.getFilePattern());
    }
    
    @Override
    public String getPerformanceMetrics() {
        DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
        return String.format("文件系统性能 - 最大扫描深度: %d, 最大文件数: %d, 缓冲区: %dKB", 
            fsConfig.getMaxScanDepth(), fsConfig.getMaxFilesPerScan(), fsConfig.getReadBufferSizeKB());
    }
    
    @Override
    public String getEstimatedSpeed() {
        return "fast"; // 文件系统直接访问速度快
    }
    
    @Override
    public String getReliabilityLevel() {
        return "high"; // 文件系统访问可靠性高
    }
    
    /**
     * 查找指定患者的DICOM文件
     */
    private List<DicomFileInfo> findDicomFiles(String patientId) throws IOException {
        // 检查缓存
        String cacheKey = "patient_" + patientId;
        long currentTime = System.currentTimeMillis();
        
        if (scanCache.containsKey(cacheKey) && (currentTime - lastScanTime) < CACHE_EXPIRE_TIME) {
            log.debug("使用缓存的扫描结果，患者ID: {}", patientId);
            return scanCache.get(cacheKey);
        }
        
        DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
        Path sourcePath = Paths.get(fsConfig.getSourcePath());
        
        List<DicomFileInfo> dicomFiles = new ArrayList<>();
        PathMatcher matcher = FileSystems.getDefault().getPathMatcher("glob:" + fsConfig.getFilePattern());
        
        Files.walkFileTree(sourcePath, EnumSet.noneOf(FileVisitOption.class), 
                fsConfig.getMaxScanDepth(), new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                if (dicomFiles.size() >= fsConfig.getMaxFilesPerScan()) {
                    return FileVisitResult.TERMINATE;
                }
                
                if (matcher.matches(file.getFileName())) {
                    try {
                        DicomFileInfo fileInfo = parseDicomFile(file);
                        if (fileInfo != null && patientId.equals(fileInfo.getPatientId())) {
                            dicomFiles.add(fileInfo);
                        }
                    } catch (Exception e) {
                        log.debug("解析DICOM文件失败: {}, 错误: {}", file, e.getMessage());
                    }
                }
                return FileVisitResult.CONTINUE;
            }
        });
        
        // 更新缓存
        scanCache.put(cacheKey, dicomFiles);
        lastScanTime = currentTime;
        
        log.info("扫描完成，患者 {} 找到 {} 个DICOM文件", patientId, dicomFiles.size());
        return dicomFiles;
    }
    
    /**
     * 解析DICOM文件获取详细信息
     */
    private DicomFileInfo parseDicomFile(Path filePath) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(filePath.toFile()))) {
            Attributes attrs = dis.readDataset(-1, -1);

            String patientId = attrs.getString(Tag.PatientID);
            String studyUID = attrs.getString(Tag.StudyInstanceUID);
            String seriesUID = attrs.getString(Tag.SeriesInstanceUID);
            String instanceUID = attrs.getString(Tag.SOPInstanceUID);
            String sopClassUID = attrs.getString(Tag.SOPClassUID);

            // 获取实例编号
            int instanceNumber = attrs.getInt(Tag.InstanceNumber, 0);

            // 获取帧数（对于多帧图像）
            int numberOfFrames = attrs.getInt(Tag.NumberOfFrames, 1);

            // 获取文件大小和修改时间
            long fileSize = Files.size(filePath);
            Date lastModified = new Date(Files.getLastModifiedTime(filePath).toMillis());

            if (StringUtils.hasText(patientId) && StringUtils.hasText(studyUID)) {
                return new DicomFileInfo(filePath, patientId, studyUID, seriesUID, instanceUID,
                                       sopClassUID, instanceNumber, numberOfFrames, fileSize, lastModified);
            }
        } catch (Exception e) {
            log.debug("解析DICOM文件失败: {}, 错误: {}", filePath, e.getMessage());
        }

        return null;
    }
    
    /**
     * 同步研究文件到dcm4chee（带完整性检查）
     */
    private boolean syncStudyFiles(String studyUID, List<DicomFileInfo> studyFiles) {
        log.info("开始同步研究: {}, 文件数: {}", studyUID, studyFiles.size());

        // 使用完整性检查服务进行检查
        List<Path> filePaths = studyFiles.stream()
                .map(DicomFileInfo::getFilePath)
                .collect(Collectors.toList());

        // 配置完整性检查
        DicomIntegrityCheckService.IntegrityCheckConfig integrityConfig =
                new DicomIntegrityCheckService.IntegrityCheckConfig();
        integrityConfig.setStrictMode(false); // 使用宽松模式
        integrityConfig.setMinCompletenessThreshold(80.0); // 80%完整性阈值
        integrityConfig.setCheckFileStability(true); // 检查文件稳定性
        integrityConfig.setAllowDuplicateInstances(false); // 不允许重复实例
        integrityConfig.setMaxMissingInstances(5); // 最多允许5个缺失实例

        // 执行完整性检查
        DicomIntegrityCheckService.IntegrityCheckResult integrityResult =
                integrityCheckService.checkStudyIntegrity(studyUID, filePaths, integrityConfig);

        log.info("研究 {} 完整性检查结果: 完整性 {}%, 问题数 {}",
            studyUID, integrityResult.getCompletenessPercentage(), integrityResult.getIssues().size());

        // 根据配置决定是否允许不完整的研究同步
        DicomHybridSyncConfig.FileSystemConfig fsConfig = hybridConfig.getFileSystem();
        boolean allowIncompleteSync = true; // 可以从配置中读取

        if (!integrityResult.isComplete() && !allowIncompleteSync) {
            log.warn("研究 {} 不完整，跳过同步。完整性: {:.1f}%",
                studyUID, integrityResult.getCompletenessPercentage());
            log.warn("完整性问题: {}", integrityResult.getIssues());
            return false;
        }

        // 如果有问题但允许不完整同步，记录警告
        if (!integrityResult.getIssues().isEmpty()) {
            log.warn("研究 {} 存在完整性问题但继续同步: {}", studyUID, integrityResult.getIssues());
        }

        // 按序列分组同步
        Map<String, List<DicomFileInfo>> seriesGroups = studyFiles.stream()
                .collect(Collectors.groupingBy(DicomFileInfo::getSeriesUID));

        int successfulSeries = 0;
        int totalSeries = seriesGroups.size();

        for (Map.Entry<String, List<DicomFileInfo>> entry : seriesGroups.entrySet()) {
            String seriesUID = entry.getKey();
            List<DicomFileInfo> seriesFiles = entry.getValue();

            try {
                if (syncSeriesFiles(seriesUID, seriesFiles)) {
                    successfulSeries++;
                }
            } catch (Exception e) {
                log.error("同步序列 {} 失败: {}", seriesUID, e.getMessage());
            }
        }

        // 如果超过80%的序列成功同步，认为研究同步成功
        boolean success = (double) successfulSeries / totalSeries >= 0.8;
        log.info("研究 {} 同步完成，成功序列: {}/{}, 结果: {}",
            studyUID, successfulSeries, totalSeries, success ? "成功" : "失败");

        return success;
    }

    /**
     * 同步序列文件
     */
    private boolean syncSeriesFiles(String seriesUID, List<DicomFileInfo> seriesFiles) {
        log.debug("开始同步序列: {}, 文件数: {}", seriesUID, seriesFiles.size());

        // 按实例编号排序，确保按正确顺序传输
        seriesFiles.sort(Comparator.comparingInt(DicomFileInfo::getInstanceNumber));

        int successCount = 0;
        for (DicomFileInfo fileInfo : seriesFiles) {
            try {
                if (storeDicomFile(fileInfo)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("存储DICOM文件失败: {}, 错误: {}", fileInfo.getFilePath(), e.getMessage());
            }
        }

        // 如果超过90%的文件成功存储，认为序列同步成功
        boolean success = (double) successCount / seriesFiles.size() >= 0.9;
        log.debug("序列 {} 同步完成，成功: {}/{}, 结果: {}",
            seriesUID, successCount, seriesFiles.size(), success ? "成功" : "失败");

        return success;
    }
    
    /**
     * 检查研究完整性
     */
    private StudyIntegrityInfo checkStudyIntegrity(String studyUID, List<DicomFileInfo> studyFiles) {
        log.info("开始检查研究完整性: {}", studyUID);

        // 按序列分组
        Map<String, List<DicomFileInfo>> seriesGroups = studyFiles.stream()
                .collect(Collectors.groupingBy(DicomFileInfo::getSeriesUID));

        List<SeriesIntegrityInfo> seriesInfoList = new ArrayList<>();
        List<String> integrityIssues = new ArrayList<>();
        long totalSize = 0;
        int totalExpectedInstances = 0;
        int totalActualInstances = 0;

        // 获取研究级别信息
        String studyDescription = getStudyDescription(studyFiles.get(0));
        Date studyDate = getStudyDate(studyFiles.get(0));

        // 检查每个序列的完整性
        for (Map.Entry<String, List<DicomFileInfo>> entry : seriesGroups.entrySet()) {
            String seriesUID = entry.getKey();
            List<DicomFileInfo> seriesFiles = entry.getValue();

            SeriesIntegrityInfo seriesInfo = checkSeriesIntegrity(seriesUID, seriesFiles);
            seriesInfoList.add(seriesInfo);

            totalSize += seriesInfo.getTotalSize();
            totalExpectedInstances += seriesInfo.getExpectedInstances();
            totalActualInstances += seriesInfo.getActualInstances();

            if (!seriesInfo.isComplete()) {
                integrityIssues.add(String.format("序列 %s (%s) 不完整: %d/%d 实例",
                    seriesUID, seriesInfo.getModality(),
                    seriesInfo.getActualInstances(), seriesInfo.getExpectedInstances()));
            }
        }

        // 检查序列数量完整性
        int expectedSeries = getExpectedSeriesCount(studyUID, studyFiles);
        int actualSeries = seriesGroups.size();

        if (actualSeries < expectedSeries) {
            integrityIssues.add(String.format("序列数量不完整: %d/%d", actualSeries, expectedSeries));
        }

        // 计算总体完整性
        boolean isComplete = integrityIssues.isEmpty();
        double completenessPercentage = totalExpectedInstances > 0 ?
            (double) totalActualInstances / totalExpectedInstances * 100 : 100;

        return new StudyIntegrityInfo(studyUID, studyDescription, studyDate,
                expectedSeries, actualSeries, seriesInfoList, isComplete,
                completenessPercentage, totalSize, integrityIssues);
    }

    /**
     * 检查序列完整性
     */
    private SeriesIntegrityInfo checkSeriesIntegrity(String seriesUID, List<DicomFileInfo> seriesFiles) {
        log.debug("检查序列完整性: {}", seriesUID);

        if (seriesFiles.isEmpty()) {
            return new SeriesIntegrityInfo(seriesUID, "", "", 0, 0,
                List.of(), List.of(), false, 0);
        }

        // 获取序列信息
        String seriesDescription = getSeriesDescription(seriesFiles.get(0));
        String modality = getModality(seriesFiles.get(0));

        // 分析实例编号
        List<Integer> instanceNumbers = seriesFiles.stream()
                .map(DicomFileInfo::getInstanceNumber)
                .filter(num -> num > 0)
                .sorted()
                .collect(Collectors.toList());

        // 检查重复实例
        Set<Integer> uniqueNumbers = new HashSet<>(instanceNumbers);
        List<Integer> duplicateNumbers = instanceNumbers.stream()
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 确定期望的实例数
        int expectedInstances = getExpectedInstanceCount(seriesUID, seriesFiles);
        int actualInstances = uniqueNumbers.size();

        // 查找缺失的实例编号
        List<Integer> missingNumbers = new ArrayList<>();
        if (!instanceNumbers.isEmpty()) {
            int minInstance = Collections.min(instanceNumbers);
            int maxInstance = Collections.max(instanceNumbers);

            for (int i = minInstance; i <= maxInstance; i++) {
                if (!uniqueNumbers.contains(i)) {
                    missingNumbers.add(i);
                }
            }
        }

        // 计算总大小
        long totalSize = seriesFiles.stream()
                .mapToLong(DicomFileInfo::getFileSize)
                .sum();

        // 判断完整性
        boolean isComplete = missingNumbers.isEmpty() && duplicateNumbers.isEmpty() &&
                           actualInstances >= expectedInstances;

        return new SeriesIntegrityInfo(seriesUID, seriesDescription, modality,
                expectedInstances, actualInstances, missingNumbers, duplicateNumbers,
                isComplete, totalSize);
    }

    /**
     * 获取期望的实例数量
     */
    private int getExpectedInstanceCount(String seriesUID, List<DicomFileInfo> seriesFiles) {
        if (seriesFiles.isEmpty()) {
            return 0;
        }

        try {
            // 尝试从DICOM文件中读取NumberOfSeriesRelatedInstances
            Path firstFile = seriesFiles.get(0).getFilePath();
            try (DicomInputStream dis = new DicomInputStream(new FileInputStream(firstFile.toFile()))) {
                Attributes attrs = dis.readDataset(-1, -1);

                // 首先尝试从序列级别获取
                int seriesRelatedInstances = attrs.getInt(Tag.NumberOfSeriesRelatedInstances, 0);
                if (seriesRelatedInstances > 0) {
                    return seriesRelatedInstances;
                }

                // 如果没有，尝试从实例编号推断
                List<Integer> instanceNumbers = seriesFiles.stream()
                        .map(DicomFileInfo::getInstanceNumber)
                        .filter(num -> num > 0)
                        .collect(Collectors.toList());

                if (!instanceNumbers.isEmpty()) {
                    return Collections.max(instanceNumbers);
                }
            }
        } catch (Exception e) {
            log.debug("获取期望实例数失败: {}", e.getMessage());
        }

        // 默认返回实际找到的文件数
        return seriesFiles.size();
    }

    /**
     * 获取期望的序列数量
     */
    private int getExpectedSeriesCount(String studyUID, List<DicomFileInfo> studyFiles) {
        if (studyFiles.isEmpty()) {
            return 0;
        }

        try {
            // 尝试从DICOM文件中读取NumberOfStudyRelatedSeries
            Path firstFile = studyFiles.get(0).getFilePath();
            try (DicomInputStream dis = new DicomInputStream(new FileInputStream(firstFile.toFile()))) {
                Attributes attrs = dis.readDataset(-1, -1);

                int studyRelatedSeries = attrs.getInt(Tag.NumberOfStudyRelatedSeries, 0);
                if (studyRelatedSeries > 0) {
                    return studyRelatedSeries;
                }
            }
        } catch (Exception e) {
            log.debug("获取期望序列数失败: {}", e.getMessage());
        }

        // 默认返回实际找到的序列数
        return (int) studyFiles.stream()
                .map(DicomFileInfo::getSeriesUID)
                .distinct()
                .count();
    }

    /**
     * 存储单个DICOM文件到dcm4chee
     */
    private boolean storeDicomFile(DicomFileInfo fileInfo) throws Exception {
        // 这里实现C-STORE操作，将文件发送到dcm4chee
        // 为了简化，这里只是模拟实现
        log.debug("存储DICOM文件: {} -> dcm4chee", fileInfo.getFilePath());

        // TODO: 实现实际的C-STORE操作
        // 1. 读取DICOM文件
        // 2. 建立与dcm4chee的关联
        // 3. 发送C-STORE请求
        // 4. 处理响应

        return true; // 临时返回成功
    }
    
    /**
     * DICOM文件信息类
     */
    private static class DicomFileInfo {
        private final Path filePath;
        private final String patientId;
        private final String studyUID;
        private final String seriesUID;
        private final String instanceUID;
        private final String sopClassUID;
        private final int instanceNumber;
        private final int numberOfFrames;
        private final long fileSize;
        private final Date lastModified;

        public DicomFileInfo(Path filePath, String patientId, String studyUID, String seriesUID,
                           String instanceUID, String sopClassUID, int instanceNumber,
                           int numberOfFrames, long fileSize, Date lastModified) {
            this.filePath = filePath;
            this.patientId = patientId;
            this.studyUID = studyUID;
            this.seriesUID = seriesUID;
            this.instanceUID = instanceUID;
            this.sopClassUID = sopClassUID;
            this.instanceNumber = instanceNumber;
            this.numberOfFrames = numberOfFrames;
            this.fileSize = fileSize;
            this.lastModified = lastModified;
        }

        public Path getFilePath() { return filePath; }
        public String getPatientId() { return patientId; }
        public String getStudyUID() { return studyUID; }
        public String getSeriesUID() { return seriesUID; }
        public String getInstanceUID() { return instanceUID; }
        public String getSopClassUID() { return sopClassUID; }
        public int getInstanceNumber() { return instanceNumber; }
        public int getNumberOfFrames() { return numberOfFrames; }
        public long getFileSize() { return fileSize; }
        public Date getLastModified() { return lastModified; }
    }

    /**
     * 序列完整性信息类
     */
    private static class SeriesIntegrityInfo {
        private final String seriesUID;
        private final String seriesDescription;
        private final String modality;
        private final int expectedInstances;
        private final int actualInstances;
        private final List<Integer> missingInstanceNumbers;
        private final List<Integer> duplicateInstanceNumbers;
        private final boolean isComplete;
        private final long totalSize;

        public SeriesIntegrityInfo(String seriesUID, String seriesDescription, String modality,
                                 int expectedInstances, int actualInstances,
                                 List<Integer> missingInstanceNumbers, List<Integer> duplicateInstanceNumbers,
                                 boolean isComplete, long totalSize) {
            this.seriesUID = seriesUID;
            this.seriesDescription = seriesDescription;
            this.modality = modality;
            this.expectedInstances = expectedInstances;
            this.actualInstances = actualInstances;
            this.missingInstanceNumbers = missingInstanceNumbers;
            this.duplicateInstanceNumbers = duplicateInstanceNumbers;
            this.isComplete = isComplete;
            this.totalSize = totalSize;
        }

        public String getSeriesUID() { return seriesUID; }
        public String getSeriesDescription() { return seriesDescription; }
        public String getModality() { return modality; }
        public int getExpectedInstances() { return expectedInstances; }
        public int getActualInstances() { return actualInstances; }
        public List<Integer> getMissingInstanceNumbers() { return missingInstanceNumbers; }
        public List<Integer> getDuplicateInstanceNumbers() { return duplicateInstanceNumbers; }
        public boolean isComplete() { return isComplete; }
        public long getTotalSize() { return totalSize; }

        public String getIntegrityReport() {
            StringBuilder report = new StringBuilder();
            report.append(String.format("序列 %s (%s):\n", seriesUID, modality));
            report.append(String.format("  描述: %s\n", seriesDescription));
            report.append(String.format("  实例数: %d/%d\n", actualInstances, expectedInstances));
            report.append(String.format("  完整性: %s\n", isComplete ? "完整" : "不完整"));
            report.append(String.format("  总大小: %.2f MB\n", totalSize / 1024.0 / 1024.0));

            if (!missingInstanceNumbers.isEmpty()) {
                report.append(String.format("  缺失实例: %s\n", missingInstanceNumbers));
            }

            if (!duplicateInstanceNumbers.isEmpty()) {
                report.append(String.format("  重复实例: %s\n", duplicateInstanceNumbers));
            }

            return report.toString();
        }
    }

    /**
     * 研究完整性信息类
     */
    private static class StudyIntegrityInfo {
        private final String studyUID;
        private final String studyDescription;
        private final Date studyDate;
        private final int expectedSeries;
        private final int actualSeries;
        private final List<SeriesIntegrityInfo> seriesInfoList;
        private final boolean isComplete;
        private final double completenessPercentage;
        private final long totalSize;
        private final List<String> integrityIssues;

        public StudyIntegrityInfo(String studyUID, String studyDescription, Date studyDate,
                                int expectedSeries, int actualSeries, List<SeriesIntegrityInfo> seriesInfoList,
                                boolean isComplete, double completenessPercentage, long totalSize,
                                List<String> integrityIssues) {
            this.studyUID = studyUID;
            this.studyDescription = studyDescription;
            this.studyDate = studyDate;
            this.expectedSeries = expectedSeries;
            this.actualSeries = actualSeries;
            this.seriesInfoList = seriesInfoList;
            this.isComplete = isComplete;
            this.completenessPercentage = completenessPercentage;
            this.totalSize = totalSize;
            this.integrityIssues = integrityIssues;
        }

        public String getStudyUID() { return studyUID; }
        public String getStudyDescription() { return studyDescription; }
        public Date getStudyDate() { return studyDate; }
        public int getExpectedSeries() { return expectedSeries; }
        public int getActualSeries() { return actualSeries; }
        public List<SeriesIntegrityInfo> getSeriesInfoList() { return seriesInfoList; }
        public boolean isComplete() { return isComplete; }
        public double getCompletenessPercentage() { return completenessPercentage; }
        public long getTotalSize() { return totalSize; }
        public List<String> getIntegrityIssues() { return integrityIssues; }

        public String getIntegrityReport() {
            StringBuilder report = new StringBuilder();
            report.append(String.format("研究完整性报告 - %s\n", studyUID));
            report.append(String.format("研究描述: %s\n", studyDescription));
            report.append(String.format("研究日期: %s\n", studyDate));
            report.append(String.format("序列数: %d/%d\n", actualSeries, expectedSeries));
            report.append(String.format("完整性: %s (%.1f%%)\n", isComplete ? "完整" : "不完整", completenessPercentage));
            report.append(String.format("总大小: %.2f MB\n", totalSize / 1024.0 / 1024.0));

            if (!integrityIssues.isEmpty()) {
                report.append("\n完整性问题:\n");
                for (String issue : integrityIssues) {
                    report.append("  - ").append(issue).append("\n");
                }
            }

            report.append("\n序列详情:\n");
            for (SeriesIntegrityInfo seriesInfo : seriesInfoList) {
                report.append(seriesInfo.getIntegrityReport()).append("\n");
            }

            return report.toString();
        }
    }

    /**
     * 获取研究描述
     */
    private String getStudyDescription(DicomFileInfo fileInfo) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(fileInfo.getFilePath().toFile()))) {
            Attributes attrs = dis.readDataset(-1, -1);
            return attrs.getString(Tag.StudyDescription, "");
        } catch (Exception e) {
            log.debug("获取研究描述失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取研究日期
     */
    private Date getStudyDate(DicomFileInfo fileInfo) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(fileInfo.getFilePath().toFile()))) {
            Attributes attrs = dis.readDataset(-1, -1);
            String studyDate = attrs.getString(Tag.StudyDate);
            String studyTime = attrs.getString(Tag.StudyTime, "000000");

            if (StringUtils.hasText(studyDate)) {
                // DICOM日期格式: YYYYMMDD
                // DICOM时间格式: HHMMSS.FFFFFF
                String dateTimeStr = studyDate + studyTime.substring(0, Math.min(6, studyTime.length()));
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
                return sdf.parse(dateTimeStr);
            }
        } catch (Exception e) {
            log.debug("获取研究日期失败: {}", e.getMessage());
        }

        return new Date();
    }

    /**
     * 获取序列描述
     */
    private String getSeriesDescription(DicomFileInfo fileInfo) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(fileInfo.getFilePath().toFile()))) {
            Attributes attrs = dis.readDataset(-1, -1);
            return attrs.getString(Tag.SeriesDescription, "");
        } catch (Exception e) {
            log.debug("获取序列描述失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取模态
     */
    private String getModality(DicomFileInfo fileInfo) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(fileInfo.getFilePath().toFile()))) {
            Attributes attrs = dis.readDataset(-1, -1);
            return attrs.getString(Tag.Modality, "");
        } catch (Exception e) {
            log.debug("获取模态失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 验证DICOM文件完整性
     */
    private boolean validateDicomFile(Path filePath) {
        try (DicomInputStream dis = new DicomInputStream(new FileInputStream(filePath.toFile()))) {
            // 尝试读取文件头
            dis.readFileMetaInformation();

            // 尝试读取数据集
            Attributes attrs = dis.readDataset(-1, -1);

            // 检查必需的标签
            String sopInstanceUID = attrs.getString(Tag.SOPInstanceUID);
            String sopClassUID = attrs.getString(Tag.SOPClassUID);

            return StringUtils.hasText(sopInstanceUID) && StringUtils.hasText(sopClassUID);
        } catch (Exception e) {
            log.debug("DICOM文件验证失败: {}, 错误: {}", filePath, e.getMessage());
            return false;
        }
    }

    /**
     * 检查文件是否正在被写入
     */
    private boolean isFileStable(Path filePath) {
        try {
            long size1 = Files.size(filePath);
            Thread.sleep(100); // 等待100ms
            long size2 = Files.size(filePath);

            return size1 == size2; // 如果大小没有变化，认为文件稳定
        } catch (Exception e) {
            log.debug("检查文件稳定性失败: {}", e.getMessage());
            return true; // 默认认为稳定
        }
    }

    /**
     * 获取文件MD5校验和
     */
    private String getFileMD5(Path filePath) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            try (FileInputStream fis = new FileInputStream(filePath.toFile());
                 java.security.DigestInputStream dis = new java.security.DigestInputStream(fis, md)) {

                byte[] buffer = new byte[8192];
                while (dis.read(buffer) != -1) {
                    // 读取文件内容计算MD5
                }

                byte[] digest = md.digest();
                StringBuilder sb = new StringBuilder();
                for (byte b : digest) {
                    sb.append(String.format("%02x", b));
                }
                return sb.toString();
            }
        } catch (Exception e) {
            log.debug("计算文件MD5失败: {}", e.getMessage());
            return "";
        }
    }
}
