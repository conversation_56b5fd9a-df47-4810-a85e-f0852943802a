package com.ruoyi.dicomSync.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DICOM混合同步配置
 * 支持多种同步方式的配置管理
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Configuration
@ConfigurationProperties(prefix = "dicom.sync.hybrid")
@Data
public class DicomHybridSyncConfig {
    
    /**
     * 是否启用混合同步模式
     */
    private boolean enabled = false;
    
    /**
     * 默认同步模式
     * 可选值：qr, filesystem, http, database, dicomweb, manual, hybrid
     */
    private String defaultMode = "qr";
    
    /**
     * 启用的同步模式列表
     * 系统将按优先级尝试这些模式
     */
    private List<String> enabledModes = new ArrayList<>();
    
    /**
     * 是否启用自动模式检测
     * 启用后系统会自动检测最佳同步方式
     */
    private boolean autoDetection = true;
    
    /**
     * 模式检测超时时间（秒）
     */
    private int detectionTimeoutSeconds = 30;
    
    /**
     * 策略选择算法
     * 可选值：priority（优先级）, performance（性能）, reliability（可靠性）
     */
    private String selectionAlgorithm = "priority";
    
    // === 文件系统模式配置 ===
    
    /**
     * 文件系统模式配置
     */
    private FileSystemConfig fileSystem = new FileSystemConfig();
    
    @Data
    public static class FileSystemConfig {
        /**
         * 是否启用文件系统模式
         */
        private boolean enabled = false;
        
        /**
         * 源PACS存储路径
         */
        private String sourcePath = "";
        
        /**
         * DICOM文件扫描模式
         * 可选值：recursive（递归扫描）, pattern（模式匹配）, index（索引文件）
         */
        private String scanMode = "recursive";
        
        /**
         * 文件扫描模式（支持通配符）
         */
        private String filePattern = "**/*.dcm";
        
        /**
         * 是否使用元数据索引
         */
        private boolean useMetadataIndex = false;
        
        /**
         * 索引文件路径
         */
        private String indexFilePath = "";
        
        /**
         * 扫描深度限制
         */
        private int maxScanDepth = 10;
        
        /**
         * 单次扫描最大文件数
         */
        private int maxFilesPerScan = 1000;
        
        /**
         * 文件读取缓冲区大小（KB）
         */
        private int readBufferSizeKB = 64;

        /**
         * 完整性检查配置
         */
        private IntegrityCheckConfig integrityCheck = new IntegrityCheckConfig();

        @Data
        public static class IntegrityCheckConfig {
            /**
             * 是否启用完整性检查
             */
            private boolean enabled = true;

            /**
             * 是否启用严格模式
             */
            private boolean strictMode = false;

            /**
             * 最小完整性阈值（百分比）
             */
            private double minCompletenessThreshold = 80.0;

            /**
             * 是否检查文件MD5
             */
            private boolean checkFileMD5 = false;

            /**
             * 是否检查文件稳定性
             */
            private boolean checkFileStability = true;

            /**
             * 文件稳定性检查等待时间（毫秒）
             */
            private long stabilityCheckWaitMs = 100;

            /**
             * 是否允许重复实例
             */
            private boolean allowDuplicateInstances = false;

            /**
             * 最大允许的缺失实例数
             */
            private int maxMissingInstances = 5;

            /**
             * 是否允许不完整研究同步
             */
            private boolean allowIncompleteSync = true;

            /**
             * 完整性检查超时时间（秒）
             */
            private int checkTimeoutSeconds = 300;
        }
    }
    
    // === HTTP API模式配置 ===
    
    /**
     * HTTP API模式配置
     */
    private HttpApiConfig httpApi = new HttpApiConfig();
    
    @Data
    public static class HttpApiConfig {
        /**
         * 是否启用HTTP API模式
         */
        private boolean enabled = false;
        
        /**
         * API基础URL
         */
        private String baseUrl = "";
        
        /**
         * 认证类型
         * 可选值：none, basic, bearer, oauth2, custom
         */
        private String authType = "none";
        
        /**
         * 用户名（用于basic认证）
         */
        private String username = "";
        
        /**
         * 密码（用于basic认证）
         */
        private String password = "";
        
        /**
         * Bearer Token（用于bearer认证）
         */
        private String bearerToken = "";
        
        /**
         * 自定义请求头
         */
        private Map<String, String> customHeaders = new HashMap<>();
        
        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeoutMs = 10000;
        
        /**
         * 读取超时时间（毫秒）
         */
        private int readTimeoutMs = 30000;
        
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
        
        /**
         * API端点配置
         */
        private ApiEndpoints endpoints = new ApiEndpoints();
        
        @Data
        public static class ApiEndpoints {
            /**
             * 查询研究列表的端点
             */
            private String listStudies = "/studies";
            
            /**
             * 下载研究的端点（支持{studyUID}占位符）
             */
            private String downloadStudy = "/studies/{studyUID}/download";
            
            /**
             * 查询患者信息的端点
             */
            private String patientInfo = "/patients/{patientId}";
            
            /**
             * 健康检查端点
             */
            private String healthCheck = "/health";
        }
    }
    
    // === 数据库直连模式配置 ===
    
    /**
     * 数据库直连模式配置
     */
    private DatabaseConfig database = new DatabaseConfig();
    
    @Data
    public static class DatabaseConfig {
        /**
         * 是否启用数据库直连模式
         */
        private boolean enabled = false;
        
        /**
         * 数据库连接URL
         */
        private String jdbcUrl = "";
        
        /**
         * 数据库用户名
         */
        private String username = "";
        
        /**
         * 数据库密码
         */
        private String password = "";
        
        /**
         * 数据库驱动类名
         */
        private String driverClassName = "com.mysql.cj.jdbc.Driver";
        
        /**
         * 连接池最大连接数
         */
        private int maxPoolSize = 5;
        
        /**
         * 查询研究的SQL语句
         */
        private String studyQuery = "SELECT study_uid, patient_id, file_path FROM studies WHERE patient_id = ?";
        
        /**
         * 查询文件路径的SQL语句
         */
        private String filePathQuery = "SELECT file_path FROM dicom_files WHERE study_uid = ?";
        
        /**
         * 查询超时时间（秒）
         */
        private int queryTimeoutSeconds = 30;
    }
    
    // === DICOM Web模式配置 ===
    
    /**
     * DICOM Web模式配置
     */
    private DicomWebConfig dicomWeb = new DicomWebConfig();
    
    @Data
    public static class DicomWebConfig {
        /**
         * 是否启用DICOM Web模式
         */
        private boolean enabled = false;
        
        /**
         * DICOM Web服务基础URL
         */
        private String baseUrl = "";
        
        /**
         * 应用实体标题
         */
        private String aeTitle = "";
        
        /**
         * 是否使用HTTPS
         */
        private boolean useHttps = false;
        
        /**
         * 认证配置
         */
        private String authType = "none";
        private String username = "";
        private String password = "";
        
        /**
         * QIDO-RS端点（查询）
         */
        private String qidoEndpoint = "/qido-rs";
        
        /**
         * WADO-RS端点（检索）
         */
        private String wadoEndpoint = "/wado-rs";
        
        /**
         * STOW-RS端点（存储）
         */
        private String stowEndpoint = "/stow-rs";
    }
}
