package com.ruoyi.dicomSync.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 系统资源监控服务
 * 用于监控系统资源使用情况，为任务调度提供决策依据
 */
@Service
@Slf4j
public class ResourceMonitorService {

    private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
    
    // 当前正在处理的高优先级请求数量
    private final AtomicInteger activeHighPriorityRequests = new AtomicInteger(0);
    
    /**
     * 增加高优先级请求计数
     */
    public void incrementHighPriorityRequests() {
        activeHighPriorityRequests.incrementAndGet();
    }
    
    /**
     * 减少高优先级请求计数
     */
    public void decrementHighPriorityRequests() {
        activeHighPriorityRequests.decrementAndGet();
    }
    
    /**
     * 获取当前高优先级请求数量
     */
    public int getActiveHighPriorityRequests() {
        return activeHighPriorityRequests.get();
    }
    
    /**
     * 检查系统是否有足够资源处理低优先级任务
     * @return 如果系统资源充足，返回true；否则返回false
     */
    public boolean hasResourcesForLowPriorityTasks() {
        // 如果有高优先级请求正在处理，暂停低优先级任务
        if (activeHighPriorityRequests.get() > 0) {
            log.info("当前有{}个高优先级请求正在处理，暂停低优先级任务", activeHighPriorityRequests.get());
            return false;
        }
        
        // 检查CPU负载
        double systemLoad = osBean.getSystemLoadAverage();
        int availableProcessors = osBean.getAvailableProcessors();
        
        // 如果系统负载超过处理器核心数的80%，暂停低优先级任务
        if (systemLoad > availableProcessors * 0.8) {
            log.info("系统负载较高 ({}/{}), 暂停低优先级任务", String.format("%.2f", systemLoad), availableProcessors);
            return false;
        }
        
        // 检查内存使用情况
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsageRatio = (double) usedMemory / maxMemory;
        
        // 如果内存使用率超过80%，暂停低优先级任务
        if (memoryUsageRatio > 0.8) {
            log.info("内存使用率较高 ({}%), 暂停低优先级任务", String.format("%.2f", memoryUsageRatio * 100));
            return false;
        }
        
        // 检查线程数量
        int threadCount = threadBean.getThreadCount();
        
        // 如果线程数量超过200，暂停低优先级任务
        if (threadCount > 200) {
            log.info("线程数量较多 ({}), 暂停低优先级任务", threadCount);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取系统资源使用情况
     * @return 系统资源使用情况的字符串表示
     */
    public String getResourceUsageInfo() {
        double systemLoad = osBean.getSystemLoadAverage();
        int availableProcessors = osBean.getAvailableProcessors();
        
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        long maxMemory = memoryBean.getHeapMemoryUsage().getMax();
        double memoryUsageRatio = (double) usedMemory / maxMemory * 100;
        
        int threadCount = threadBean.getThreadCount();
        
        return String.format(
            "系统资源使用情况: CPU负载=%.2f/%d, 内存使用率=%.2f%%, 线程数=%d, 高优先级请求数=%d",
            systemLoad, availableProcessors, memoryUsageRatio, threadCount, activeHighPriorityRequests.get()
        );
    }
}
