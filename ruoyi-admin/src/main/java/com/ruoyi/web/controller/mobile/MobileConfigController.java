package com.ruoyi.web.controller.mobile;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 移动端配置控制器
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/mobile/config")
public class MobileConfigController extends BaseController
{
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取登录页配置信息
     *
     * @return 登录页配置
     */
    @GetMapping("/login")
    public AjaxResult getLoginConfig()
    {
        try {
            Map<String, String> config = new HashMap<>();

            // 获取各项配置
            config.put("hospitalName", configService.selectConfigByKey("mobile.login.hospitalName"));
            config.put("systemName", configService.selectConfigByKey("mobile.login.systemName"));
            config.put("footerText", configService.selectConfigByKey("mobile.login.footerText"));
            config.put("welcomeText", configService.selectConfigByKey("mobile.login.welcomeText"));
            config.put("subTitle", configService.selectConfigByKey("mobile.login.subTitle"));

            // 设置默认值（如果配置为空）
            if (config.get("hospitalName") == null || config.get("hospitalName").isEmpty()) {
                config.put("hospitalName", "医院名称");
            }
            if (config.get("systemName") == null || config.get("systemName").isEmpty()) {
                config.put("systemName", "云影像服务平台");
            }
            if (config.get("footerText") == null || config.get("footerText").isEmpty()) {
                config.put("footerText", "本系统提供检查报告查询、影像查看等服务");
            }
            if (config.get("welcomeText") == null || config.get("welcomeText").isEmpty()) {
                config.put("welcomeText", "欢迎使用");
            }
            if (config.get("subTitle") == null || config.get("subTitle").isEmpty()) {
                config.put("subTitle", "请使用预留手机号登录，查看您的检查报告和影像");
            }

            return AjaxResult.success("获取成功", config);
        }
        catch (Exception e)
        {
            log.error("获取登录页配置失败", e);
            return AjaxResult.error("获取登录页配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取影像API配置信息
     *
     * @return 影像API配置
     */
    @GetMapping("/image")
    public AjaxResult getImageConfig()
    {
        try {
            Map<String, String> config = new HashMap<>();

            // 获取影像API配置
            config.put("apiUrl", configService.selectConfigByKey("mobile.image.apiUrl"));
            config.put("viewerUrl", configService.selectConfigByKey("mobile.image.viewerUrl"));

            // 设置默认值（如果配置为空）
            if (config.get("apiUrl") == null || config.get("apiUrl").isEmpty()) {
                config.put("apiUrl", "https://yyx.xinguokeji.cn");
            }
            if (config.get("viewerUrl") == null || config.get("viewerUrl").isEmpty()) {
                config.put("viewerUrl", "https://yyx.xinguokeji.cn/mobile/dicomViewer.html");
            }

            return AjaxResult.success("获取成功", config);
        }
        catch (Exception e)
        {
            log.error("获取影像API配置失败", e);
            return AjaxResult.error("获取影像API配置失败：" + e.getMessage());
        }
    }
}
