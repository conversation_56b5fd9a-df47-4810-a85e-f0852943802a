package com.ruoyi.web.controller.dicom;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.dicomSync.enums.DicomSyncMode;
import com.ruoyi.dicomSync.service.DicomHybridSyncService;
import com.ruoyi.dicomSync.strategy.DicomSyncStrategy;
import com.ruoyi.dicomSync.strategy.DicomSyncStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * DICOM混合同步测试控制器
 * 用于测试和验证混合同步系统的功能
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/dicom/hybrid/test")
@Slf4j
public class DicomHybridTestController extends BaseController {
    
    @Autowired
    private DicomHybridSyncService hybridSyncService;
    
    @Autowired
    private DicomSyncStrategyFactory strategyFactory;
    
    /**
     * 测试策略工厂功能
     */
    @GetMapping("/factory")
    public AjaxResult testStrategyFactory() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试获取默认策略
            DicomSyncStrategy defaultStrategy = strategyFactory.getDefaultStrategy();
            result.put("defaultStrategy", defaultStrategy.getSupportedMode().getName());
            
            // 测试获取指定策略
            DicomSyncStrategy qrStrategy = strategyFactory.getStrategy(DicomSyncMode.QR_SERVICE);
            result.put("qrStrategyAvailable", qrStrategy != null);
            
            DicomSyncStrategy fsStrategy = strategyFactory.getStrategy(DicomSyncMode.FILE_SYSTEM);
            result.put("fileSystemStrategyAvailable", fsStrategy != null);
            
            // 测试策略选择
            DicomSyncStrategy bestStrategy = strategyFactory.selectBestStrategy("TEST_PATIENT");
            result.put("bestStrategy", bestStrategy.getSupportedMode().getName());
            
            // 获取统计信息
            Map<String, Object> stats = strategyFactory.getStrategyStatistics();
            result.put("statistics", stats);
            
            return AjaxResult.success("策略工厂测试完成", result);
        } catch (Exception e) {
            log.error("策略工厂测试失败", e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试策略可用性
     */
    @GetMapping("/availability/{mode}")
    public AjaxResult testStrategyAvailability(@PathVariable String mode, 
                                             @RequestParam(required = false) String patientId) {
        try {
            DicomSyncStrategy strategy = strategyFactory.getStrategy(mode);
            if (strategy == null) {
                return AjaxResult.error("策略不存在: " + mode);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("mode", mode);
            result.put("strategyName", strategy.getSupportedMode().getName());
            
            String testPatientId = patientId != null ? patientId : "TEST_PATIENT";
            boolean available = strategy.isAvailable(testPatientId);
            result.put("available", available);
            
            if (!available) {
                String reason = strategy.getUnavailableReason(testPatientId);
                result.put("unavailableReason", reason);
            }
            
            // 测试连接
            boolean connected = strategy.testConnection();
            result.put("connected", connected);
            
            // 获取配置信息
            result.put("configuration", strategy.getConfigurationInfo());
            result.put("performance", strategy.getPerformanceMetrics());
            result.put("estimatedSpeed", strategy.getEstimatedSpeed());
            result.put("reliabilityLevel", strategy.getReliabilityLevel());
            
            return AjaxResult.success("策略可用性测试完成", result);
        } catch (Exception e) {
            log.error("策略可用性测试失败", e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试混合同步服务
     */
    @PostMapping("/sync/{patientId}")
    public AjaxResult testHybridSync(@PathVariable String patientId, 
                                   @RequestParam(required = false) String mode) {
        try {
            log.info("测试混合同步，患者ID: {}, 模式: {}", patientId, mode);
            
            DicomSyncMode syncMode = null;
            if (mode != null && !mode.trim().isEmpty()) {
                syncMode = DicomSyncMode.fromCode(mode);
            }
            
            // 先测试查询功能
            Map<String, Object> result = new HashMap<>();
            
            try {
                var studyUIDs = hybridSyncService.queryStudyUIDs(patientId, syncMode);
                result.put("querySuccess", true);
                result.put("studyCount", studyUIDs.size());
                result.put("studyUIDs", studyUIDs);
            } catch (Exception e) {
                result.put("querySuccess", false);
                result.put("queryError", e.getMessage());
            }
            
            // 再测试同步功能（这里只是模拟，不执行实际同步）
            try {
                // 获取选择的策略信息
                DicomSyncStrategy selectedStrategy = strategyFactory.selectBestStrategy(patientId);
                result.put("selectedStrategy", selectedStrategy.getSupportedMode().getName());
                result.put("strategyAvailable", selectedStrategy.isAvailable(patientId));
                
                if (!selectedStrategy.isAvailable(patientId)) {
                    result.put("unavailableReason", selectedStrategy.getUnavailableReason(patientId));
                }
                
                // 注意：这里不执行实际同步，只是测试策略选择
                result.put("syncTestNote", "仅测试策略选择，未执行实际同步");
                
            } catch (Exception e) {
                result.put("syncTestError", e.getMessage());
            }
            
            return AjaxResult.success("混合同步测试完成", result);
        } catch (Exception e) {
            log.error("混合同步测试失败", e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试所有模式
     */
    @GetMapping("/all-modes")
    public AjaxResult testAllModes() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            for (DicomSyncMode mode : DicomSyncMode.values()) {
                Map<String, Object> modeInfo = new HashMap<>();
                
                DicomSyncStrategy strategy = strategyFactory.getStrategy(mode);
                if (strategy != null) {
                    modeInfo.put("available", strategy.isAvailable("TEST_PATIENT"));
                    modeInfo.put("connected", strategy.testConnection());
                    modeInfo.put("configuration", strategy.getConfigurationInfo());
                    modeInfo.put("speed", strategy.getEstimatedSpeed());
                    modeInfo.put("reliability", strategy.getReliabilityLevel());
                    
                    if (!strategy.isAvailable("TEST_PATIENT")) {
                        modeInfo.put("reason", strategy.getUnavailableReason("TEST_PATIENT"));
                    }
                } else {
                    modeInfo.put("available", false);
                    modeInfo.put("reason", "策略实现不存在");
                }
                
                result.put(mode.getCode(), modeInfo);
            }
            
            return AjaxResult.success("所有模式测试完成", result);
        } catch (Exception e) {
            log.error("所有模式测试失败", e);
            return AjaxResult.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public AjaxResult getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 获取策略统计
            status.put("statistics", hybridSyncService.getStrategyStatistics());
            
            // 测试连接状态
            status.put("connections", hybridSyncService.testAllConnections());
            
            // 获取可用策略
            status.put("availableStrategies", hybridSyncService.getAvailableStrategies("TEST_PATIENT"));
            
            return AjaxResult.success("系统状态获取成功", status);
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置测试环境
     */
    @PostMapping("/reset")
    public AjaxResult resetTestEnvironment() {
        try {
            log.info("重置测试环境");
            
            // 这里可以添加重置逻辑，比如清理缓存等
            // 目前只是记录日志
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "测试环境已重置");
            result.put("timestamp", System.currentTimeMillis());
            
            return AjaxResult.success("重置成功", result);
        } catch (Exception e) {
            log.error("重置测试环境失败", e);
            return AjaxResult.error("重置失败: " + e.getMessage());
        }
    }
}
