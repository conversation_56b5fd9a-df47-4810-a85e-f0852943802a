package com.ruoyi.diagnosis.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.MinioFileUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * MinIO配置管理控制器
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/minio/config")
public class MinioConfigController extends BaseController {

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private MinioFileUtils minioFileUtils;

    /**
     * 获取MinIO配置
     */
    @PreAuthorize("@ss.hasPermi('minio:config:query')")
    @GetMapping("/list")
    public AjaxResult getConfig() {
        Map<String, String> config = new HashMap<>();
        
        // 获取MinIO相关配置
        config.put("endpoint", sysConfigService.selectConfigByKey("minio.endpoint"));
        config.put("accessKey", sysConfigService.selectConfigByKey("minio.accessKey"));
        config.put("secretKey", maskSensitiveInfo(sysConfigService.selectConfigByKey("minio.secretKey")));
        config.put("bucketName", sysConfigService.selectConfigByKey("minio.bucketName"));
        config.put("urlPrefix", sysConfigService.selectConfigByKey("minio.urlPrefix"));
        
        return success(config);
    }

    /**
     * 更新MinIO配置
     */
    @PreAuthorize("@ss.hasPermi('minio:config:edit')")
    @Log(title = "MinIO配置", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult updateConfig(@RequestBody Map<String, String> config) {
        try {
            // 更新各个配置项
            if (config.containsKey("endpoint")) {
                sysConfigService.updateConfigByKey("minio.endpoint", config.get("endpoint"));
            }
            if (config.containsKey("accessKey")) {
                sysConfigService.updateConfigByKey("minio.accessKey", config.get("accessKey"));
            }
            if (config.containsKey("secretKey") && !isMaskedValue(config.get("secretKey"))) {
                sysConfigService.updateConfigByKey("minio.secretKey", config.get("secretKey"));
            }
            if (config.containsKey("bucketName")) {
                sysConfigService.updateConfigByKey("minio.bucketName", config.get("bucketName"));
            }
            if (config.containsKey("urlPrefix")) {
                sysConfigService.updateConfigByKey("minio.urlPrefix", config.get("urlPrefix"));
            }
            
            return success("MinIO配置更新成功");
        } catch (Exception e) {
            logger.error("更新MinIO配置失败", e);
            return error("配置更新失败: " + e.getMessage());
        }
    }

    /**
     * 测试MinIO连接
     */
    @PreAuthorize("@ss.hasPermi('minio:config:test')")
    @PostMapping("/test")
    public AjaxResult testConnection() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取配置信息
            String endpoint = sysConfigService.selectConfigByKey("minio.endpoint");
            String bucketName = sysConfigService.selectConfigByKey("minio.bucketName");
            
            result.put("endpoint", endpoint);
            result.put("bucketName", bucketName);
            
            // 测试连接（这里可以实现具体的连接测试逻辑）
            boolean connectionSuccess = testMinioConnection();
            result.put("connectionSuccess", connectionSuccess);
            
            if (connectionSuccess) {
                result.put("message", "MinIO连接测试成功");
            } else {
                result.put("message", "MinIO连接测试失败");
            }
            
            return success(result);
        } catch (Exception e) {
            logger.error("测试MinIO连接失败", e);
            return error("连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取MinIO存储统计信息
     */
    @PreAuthorize("@ss.hasPermi('minio:config:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 这里可以实现获取MinIO存储统计信息的逻辑
            // 例如：存储桶大小、文件数量等
            statistics.put("bucketName", sysConfigService.selectConfigByKey("minio.bucketName"));
            statistics.put("endpoint", sysConfigService.selectConfigByKey("minio.endpoint"));
            statistics.put("status", "运行中");
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取MinIO统计信息失败", e);
            return error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 重置MinIO配置为默认值
     */
    @PreAuthorize("@ss.hasPermi('minio:config:edit')")
    @Log(title = "MinIO配置", businessType = BusinessType.UPDATE)
    @PostMapping("/reset")
    public AjaxResult resetConfig() {
        try {
            // 重置为默认值
            sysConfigService.updateConfigByKey("minio.endpoint", "http://localhost:9000");
            sysConfigService.updateConfigByKey("minio.accessKey", "minioadmin");
            sysConfigService.updateConfigByKey("minio.secretKey", "minioadmin");
            sysConfigService.updateConfigByKey("minio.bucketName", "pacs-reports");
            sysConfigService.updateConfigByKey("minio.urlPrefix", "http://localhost:9000/pacs-reports/");
            
            return success("MinIO配置已重置为默认值");
        } catch (Exception e) {
            logger.error("重置MinIO配置失败", e);
            return error("配置重置失败: " + e.getMessage());
        }
    }

    /**
     * 掩码敏感信息
     */
    private String maskSensitiveInfo(String value) {
        if (StringUtils.isEmpty(value)) {
            return "";
        }
        if (value.length() <= 4) {
            return "****";
        }
        return value.substring(0, 2) + "****" + value.substring(value.length() - 2);
    }

    /**
     * 判断是否为掩码值
     */
    private boolean isMaskedValue(String value) {
        return StringUtils.isNotEmpty(value) && value.contains("****");
    }

    /**
     * 测试MinIO连接
     */
    private boolean testMinioConnection() {
        try {
            // 这里可以实现具体的MinIO连接测试逻辑
            // 例如：尝试列出存储桶或上传一个测试文件
            return true; // 简化实现，实际应该进行真实的连接测试
        } catch (Exception e) {
            logger.warn("MinIO连接测试失败: {}", e.getMessage());
            return false;
        }
    }
}
