package com.ruoyi.diagnosis.mapper;

import java.util.List;
import com.ruoyi.diagnosis.domain.DiagnoseDictCategory;

/**
 * 诊断字典Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
public interface DiagnoseDictCategoryMapper 
{
    /**
     * 查询诊断字典
     * 
     * @param id 诊断字典主键
     * @return 诊断字典
     */
    public DiagnoseDictCategory selectDiagnoseDictCategoryById(Long id);

    /**
     * 查询诊断字典列表
     * 
     * @param diagnoseDictCategory 诊断字典
     * @return 诊断字典集合
     */
    public List<DiagnoseDictCategory> selectDiagnoseDictCategoryList(DiagnoseDictCategory diagnoseDictCategory);

    /**
     * 新增诊断字典
     * 
     * @param diagnoseDictCategory 诊断字典
     * @return 结果
     */
    public int insertDiagnoseDictCategory(DiagnoseDictCategory diagnoseDictCategory);

    /**
     * 修改诊断字典
     * 
     * @param diagnoseDictCategory 诊断字典
     * @return 结果
     */
    public int updateDiagnoseDictCategory(DiagnoseDictCategory diagnoseDictCategory);

    /**
     * 删除诊断字典
     * 
     * @param id 诊断字典主键
     * @return 结果
     */
    public int deleteDiagnoseDictCategoryById(Long id);

    /**
     * 批量删除诊断字典
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDiagnoseDictCategoryByIds(Long[] ids);
}
