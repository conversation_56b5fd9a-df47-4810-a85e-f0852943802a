package com.ruoyi.diagnosis.mapper;

import java.util.List;
import com.ruoyi.diagnosis.domain.Diagnosis;

/**
 * 诊断Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface DiagnosisMapper 
{

    Diagnosis getByCheckId(Long checkId);
    /**
     * 查询诊断
     * 
     * @param id 诊断主键
     * @return 诊断
     */
    public Diagnosis selectDiagnosisById(Long id);

    /**
     * 查询诊断列表
     * 
     * @param diagnosis 诊断
     * @return 诊断集合
     */
    public List<Diagnosis> selectDiagnosisList(Diagnosis diagnosis);

    /**
     * 新增诊断
     * 
     * @param diagnosis 诊断
     * @return 结果
     */
    public int insertDiagnosis(Diagnosis diagnosis);

    /**
     * 修改诊断
     * 
     * @param diagnosis 诊断
     * @return 结果
     */
    public int updateDiagnosis(Diagnosis diagnosis);

    /**
     * 删除诊断
     * 
     * @param id 诊断主键
     * @return 结果
     */
    public int deleteDiagnosisById(Long id);

    /**
     * 批量删除诊断
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDiagnosisByIds(Long[] ids);

    /**
     * 查询已审核但未生成PDF的诊断记录
     *
     * @return 诊断集合
     */
    public List<Diagnosis> selectAuditedWithoutPdf();
}
