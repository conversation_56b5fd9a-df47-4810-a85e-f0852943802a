package com.ruoyi.diagnosis.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.diagnosis.domain.Template;

/**
 * 报告模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
public interface TemplateMapper 
{
    /**
     * 查询报告模板
     * 
     * @param id 报告模板主键
     * @return 报告模板
     */
    public Template selectTemplateById(Long id);

    /**
     * 查询报告模板列表
     * 
     * @param template 报告模板
     * @return 报告模板集合
     */
    public List<Template> selectTemplateList(Template template);

    /**
     * 新增报告模板
     * 
     * @param template 报告模板
     * @return 结果
     */
    public int insertTemplate(Template template);

    /**
     * 修改报告模板
     * 
     * @param template 报告模板
     * @return 结果
     */
    public int updateTemplate(Template template);

    /**
     * 删除报告模板
     * 
     * @param id 报告模板主键
     * @return 结果
     */
    public int deleteTemplateById(Long id);

    /**
     * 批量删除报告模板
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTemplateByIds(Long[] ids);

    /**
     * 根据检查类型查询模板列表
     *
     * @param modalityType 检查类型
     * @return 模板列表
     */
    public List<Template> selectTemplatesByModalityType(String modalityType);

    /**
     * 根据检查类型和身体部位查询模板
     *
     * @param modalityType 检查类型
     * @param bodyPart 身体部位
     * @return 模板列表
     */
    public List<Template> selectTemplatesByModalityAndBodyPart(@Param("modalityType") String modalityType, @Param("bodyPart") String bodyPart);

    /**
     * 查询公共模板
     *
     * @return 公共模板列表
     */
    public List<Template> selectPublicTemplates();

    /**
     * 查询用户模板
     *
     * @param createBy 创建者
     * @return 用户模板列表
     */
    public List<Template> selectUserTemplates(String createBy);

    /**
     * 更新模板使用次数
     *
     * @param id 模板ID
     * @return 结果
     */
    public int updateTemplateUsageCount(Long id);

    /**
     * 查询默认模板
     *
     * @param modalityType 检查类型
     * @return 默认模板
     */
    public Template selectDefaultTemplate(@Param("modalityType") String modalityType);
}
