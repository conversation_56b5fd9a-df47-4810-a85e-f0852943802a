package com.ruoyi.diagnosis.mapper;

import java.util.List;
import com.ruoyi.diagnosis.domain.DiagnosisRecord;

/**
 * 诊断记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface DiagnosisRecordMapper
{
    /**
     * 查询诊断记录
     *
     * @param id 诊断记录主键
     * @return 诊断记录
     */
    public DiagnosisRecord selectDiagnosisRecordById(Long id);

    /**
     * 查询诊断记录列表
     *
     * @param diagnosisRecord 诊断记录
     * @return 诊断记录集合
     */
    public List<DiagnosisRecord> selectDiagnosisRecordList(DiagnosisRecord diagnosisRecord);

    /**
     * 根据诊断ID查询诊断记录列表
     *
     * @param diagnosisId 诊断ID
     * @return 诊断记录集合
     */
    public List<DiagnosisRecord> listByDiagnosisId(Long diagnosisId);

    /**
     * 新增诊断记录
     *
     * @param diagnosisRecord 诊断记录
     * @return 结果
     */
    public int insertDiagnosisRecord(DiagnosisRecord diagnosisRecord);

    /**
     * 修改诊断记录
     *
     * @param diagnosisRecord 诊断记录
     * @return 结果
     */
    public int updateDiagnosisRecord(DiagnosisRecord diagnosisRecord);

    /**
     * 删除诊断记录
     *
     * @param id 诊断记录主键
     * @return 结果
     */
    public int deleteDiagnosisRecordById(Long id);

    /**
     * 批量删除诊断记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDiagnosisRecordByIds(Long[] ids);
}
