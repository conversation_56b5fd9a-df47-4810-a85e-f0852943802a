package com.ruoyi.diagnosis.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 模板数据库迁移工具
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Component
public class TemplateMigrationUtil {

    private static final Logger log = LoggerFactory.getLogger(TemplateMigrationUtil.class);

    @Autowired
    @Qualifier("mainJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    /**
     * 执行数据库迁移
     */
    public void executeMigration() {
        try {
            log.info("开始执行模板表迁移...");
            
            // 1. 检查旧表是否存在并备份数据
            backupOldTemplateData();
            
            // 2. 删除旧表
            dropOldTable();
            
            // 3. 创建新的诊断模板表
            createDiagnosisTemplateTable();
            
            // 4. 创建模板分类表
            createTemplateCategoryTable();
            
            // 5. 插入预置数据
            insertPresetData();
            
            log.info("模板表迁移完成！");
            
        } catch (Exception e) {
            log.error("模板表迁移失败: {}", e.getMessage(), e);
            throw new RuntimeException("模板表迁移失败", e);
        }
    }

    /**
     * 备份旧模板数据
     */
    private void backupOldTemplateData() {
        try {
            // 检查旧表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'template'";
            Integer count = jdbcTemplate.queryForObject(checkTableSql, Integer.class);
            
            if (count != null && count > 0) {
                log.info("发现旧的template表，开始备份数据...");
                
                // 创建备份表
                String createBackupSql = "CREATE TABLE template_backup_" + System.currentTimeMillis() + " AS SELECT * FROM template";
                jdbcTemplate.execute(createBackupSql);
                
                // 查询旧数据
                String queryOldDataSql = "SELECT * FROM template";
                List<Map<String, Object>> oldData = jdbcTemplate.queryForList(queryOldDataSql);
                log.info("备份了 {} 条旧模板数据", oldData.size());
            } else {
                log.info("未发现旧的template表，跳过备份步骤");
            }
        } catch (Exception e) {
            log.warn("备份旧数据时出错: {}", e.getMessage());
        }
    }

    /**
     * 删除旧表
     */
    private void dropOldTable() {
        try {
            String dropSql = "DROP TABLE IF EXISTS template";
            jdbcTemplate.execute(dropSql);
            log.info("已删除旧的template表");
        } catch (Exception e) {
            log.warn("删除旧表时出错: {}", e.getMessage());
        }
    }

    /**
     * 创建新的诊断模板表
     */
    private void createDiagnosisTemplateTable() {
        String createSql = """
            CREATE TABLE diagnosis_template (
              id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
              name varchar(100) NOT NULL COMMENT '模板名称',
              category varchar(50) NOT NULL COMMENT '模板分类：findings=影像所见, opinion=影像意见, complete=完整报告',
              modality_type varchar(50) DEFAULT NULL COMMENT '适用检查类型(CT/MRI/DR/US/OTHER)',
              body_part varchar(100) DEFAULT NULL COMMENT '适用身体部位',
              title varchar(200) DEFAULT NULL COMMENT '诊断标题',
              content text NOT NULL COMMENT '模板内容',
              conclusion text DEFAULT NULL COMMENT '诊断结论',
              keywords varchar(500) DEFAULT NULL COMMENT '关键词，用逗号分隔，便于搜索',
              usage_count int(11) DEFAULT 0 COMMENT '使用次数统计',
              is_public char(1) DEFAULT '0' COMMENT '是否公开(0私有 1公开)',
              is_default char(1) DEFAULT '0' COMMENT '是否默认模板(0否 1是)',
              enable_flag char(1) DEFAULT '1' COMMENT '启用状态(0禁用 1启用)',
              sort_order int(11) DEFAULT 0 COMMENT '排序序号',
              remark varchar(500) DEFAULT NULL COMMENT '备注信息',
              create_by varchar(64) DEFAULT '' COMMENT '创建者',
              create_time datetime DEFAULT NULL COMMENT '创建时间',
              update_by varchar(64) DEFAULT '' COMMENT '更新者',
              update_time datetime DEFAULT NULL COMMENT '更新时间',
              del_flag char(1) DEFAULT '0' COMMENT '删除标志(0存在 1删除)',
              PRIMARY KEY (id),
              KEY idx_category (category),
              KEY idx_modality_type (modality_type),
              KEY idx_body_part (body_part),
              KEY idx_is_public (is_public),
              KEY idx_enable_flag (enable_flag),
              KEY idx_usage_count (usage_count),
              KEY idx_create_by (create_by)
            ) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='诊断模板表'
            """;
        
        jdbcTemplate.execute(createSql);
        log.info("已创建diagnosis_template表");
    }

    /**
     * 创建模板分类表
     */
    private void createTemplateCategoryTable() {
        String createSql = """
            CREATE TABLE IF NOT EXISTS template_category (
              id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
              category_code varchar(50) NOT NULL COMMENT '分类代码',
              category_name varchar(100) NOT NULL COMMENT '分类名称',
              parent_code varchar(50) DEFAULT NULL COMMENT '父分类代码',
              sort_order int(11) DEFAULT 0 COMMENT '排序序号',
              enable_flag char(1) DEFAULT '1' COMMENT '启用状态',
              remark varchar(200) DEFAULT NULL COMMENT '备注',
              create_time datetime DEFAULT NULL COMMENT '创建时间',
              PRIMARY KEY (id),
              UNIQUE KEY uk_category_code (category_code),
              KEY idx_parent_code (parent_code)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板分类字典表'
            """;
        
        jdbcTemplate.execute(createSql);
        log.info("已创建template_category表");
    }

    /**
     * 插入预置数据
     */
    private void insertPresetData() {
        // 插入模板数据
        insertTemplateData();
        
        // 插入分类数据
        insertCategoryData();
    }

    /**
     * 插入模板数据
     */
    private void insertTemplateData() {
        String[] templateSqls = {
            // CT胸部模板
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('CT胸部正常模板', 'findings', 'CT', '胸部', 'CT胸部平扫', '双肺纹理清晰，未见明显异常密度影。心影大小形态正常。纵隔居中，气管通畅。胸膜光滑，未见胸腔积液征象。', '双肺未见明显异常。', 'CT,胸部,正常,双肺', '1', '1', '1', 1, 'admin', NOW())",
            
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('CT胸部肺炎模板', 'findings', 'CT', '胸部', 'CT胸部平扫', '双肺可见多发斑片状密度增高影，边界模糊，以下叶为著。部分病灶内可见支气管充气征。心影大小形态正常。纵隔无明显异常。', '考虑肺炎可能，建议结合临床。', 'CT,胸部,肺炎,感染', '1', '0', '1', 2, 'admin', NOW())",
            
            // MRI头部模板
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('MRI头部正常模板', 'findings', 'MRI', '头部', 'MRI头部平扫+增强', '双侧大脑半球脑实质信号正常，灰白质分界清楚。脑室系统大小形态正常，中线结构居中。小脑及脑干未见异常信号。', '颅脑MRI未见明显异常。', 'MRI,头部,正常,颅脑', '1', '1', '1', 3, 'admin', NOW())",
            
            // DR胸部模板
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('DR胸部正常模板', 'findings', 'DR', '胸部', 'DR胸部正位片', '双肺纹理清晰，肺野透亮度正常，未见明显异常阴影。心影大小正常，纵隔居中。双侧膈面光滑，肋膈角锐利。', '胸部DR未见明显异常。', 'DR,胸部,正常,X线', '1', '1', '1', 4, 'admin', NOW())",
            
            // 超声腹部模板
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('US腹部正常模板', 'findings', 'US', '腹部', '腹部超声检查', '肝脏大小形态正常，实质回声均匀，肝内管道结构清晰。胆囊大小形态正常，囊壁光滑，囊内未见异常回声。胰腺显示清楚，回声均匀。双肾大小形态正常，实质回声均匀。', '腹部超声未见明显异常。', '超声,腹部,正常,肝胆胰脾肾', '1', '1', '1', 5, 'admin', NOW())",
            
            // 常用意见模板
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('建议复查模板', 'opinion', 'OTHER', '通用', '', '', '建议定期复查，如有不适及时就诊。', '复查,随访,定期', '1', '1', '1', 10, 'admin', NOW())",
            
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('建议进一步检查模板', 'opinion', 'OTHER', '通用', '', '', '建议结合临床，必要时进一步检查。', '进一步检查,临床,结合', '1', '1', '1', 11, 'admin', NOW())",
            
            "INSERT INTO diagnosis_template (name, category, modality_type, body_part, title, content, conclusion, keywords, is_public, is_default, enable_flag, sort_order, create_by, create_time) VALUES ('急诊处理模板', 'opinion', 'OTHER', '通用', '', '', '建议急诊处理，密切观察病情变化。', '急诊,处理,观察', '1', '0', '1', 12, 'admin', NOW())"
        };
        
        for (String sql : templateSqls) {
            try {
                jdbcTemplate.execute(sql);
            } catch (Exception e) {
                log.warn("插入模板数据失败: {}", e.getMessage());
            }
        }
        
        log.info("已插入预置模板数据");
    }

    /**
     * 插入分类数据
     */
    private void insertCategoryData() {
        String[] categorySqls = {
            // 主分类
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('findings', '影像所见', NULL, 1, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('opinion', '影像意见', NULL, 2, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('complete', '完整报告', NULL, 3, '1', NOW())",
            
            // 检查类型分类
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('CT', 'CT检查', NULL, 10, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('MRI', 'MRI检查', NULL, 11, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('DR', 'DR检查', NULL, 12, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('US', '超声检查', NULL, 13, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('OTHER', '其他检查', NULL, 14, '1', NOW())",
            
            // 身体部位分类
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('head', '头部', NULL, 20, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('chest', '胸部', NULL, 21, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('abdomen', '腹部', NULL, 22, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('pelvis', '盆腔', NULL, 23, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('spine', '脊柱', NULL, 24, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('limbs', '四肢', NULL, 25, '1', NOW())",
            "INSERT INTO template_category (category_code, category_name, parent_code, sort_order, enable_flag, create_time) VALUES ('general', '全身', NULL, 26, '1', NOW())"
        };
        
        for (String sql : categorySqls) {
            try {
                jdbcTemplate.execute(sql);
            } catch (Exception e) {
                log.warn("插入分类数据失败: {}", e.getMessage());
            }
        }
        
        log.info("已插入预置分类数据");
    }
}
