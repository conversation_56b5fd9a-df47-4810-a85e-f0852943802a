package com.ruoyi.ai.mapper;

import java.util.List;
import com.ruoyi.ai.domain.AiAnalysis;

/**
 * AI影像分析Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-10
 */
public interface AiAnalysisMapper
{
    /**
     * 查询AI影像分析
     * 
     * @param id AI影像分析ID
     * @return AI影像分析
     */
    public AiAnalysis selectAiAnalysisById(Long id);

    /**
     * 查询AI影像分析列表
     * 
     * @param aiAnalysis AI影像分析
     * @return AI影像分析集合
     */
    public List<AiAnalysis> selectAiAnalysisList(AiAnalysis aiAnalysis);

    /**
     * 新增AI影像分析
     * 
     * @param aiAnalysis AI影像分析
     * @return 结果
     */
    public int insertAiAnalysis(AiAnalysis aiAnalysis);

    /**
     * 修改AI影像分析
     * 
     * @param aiAnalysis AI影像分析
     * @return 结果
     */
    public int updateAiAnalysis(AiAnalysis aiAnalysis);

    /**
     * 删除AI影像分析
     * 
     * @param id AI影像分析ID
     * @return 结果
     */
    public int deleteAiAnalysisById(Long id);

    /**
     * 批量删除AI影像分析
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteAiAnalysisByIds(Long[] ids);
    
    /**
     * 根据检查实例UID查询AI影像分析列表
     * 
     * @param studyInstanceUid 检查实例UID
     * @return AI影像分析集合
     */
    public List<AiAnalysis> selectAiAnalysisByStudyInstanceUid(String studyInstanceUid);
}
