package com.ruoyi.ai.service.impl;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.ruoyi.ai.service.BailianApiService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.datasync.domain.PacsPatientStudy;
import com.ruoyi.datasync.mapper.PacsPatientStudyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.ai.mapper.AiReportAnalysisMapper;
import com.ruoyi.ai.domain.AiReportAnalysis;
import com.ruoyi.ai.service.IAiReportAnalysisService;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * AI报告解析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-15
 */
@Slf4j
@Service
public class AiReportAnalysisServiceImpl implements IAiReportAnalysisService 
{
    @Autowired
    private AiReportAnalysisMapper aiReportAnalysisMapper;

    @Autowired
    private PacsPatientStudyMapper pacsPatientStudyMapper;

    @Autowired
    private BailianApiService bailianApiService;

    /**
     * 查询AI报告解析
     * 
     * @param id AI报告解析主键
     * @return AI报告解析
     */
    @Override
    public AiReportAnalysis selectAiReportAnalysisById(Long id)
    {
        return aiReportAnalysisMapper.selectAiReportAnalysisById(id);
    }

    /**
     * 根据检查记录ID查询AI报告解析
     *
     * @param studyId 检查记录ID
     * @return AI报告解析
     */
    @Override
    public AiReportAnalysis selectAiReportAnalysisByStudyId(Long studyId)
    {
        return aiReportAnalysisMapper.selectAiReportAnalysisByStudyId(studyId);
    }

    /**
     * 根据检查号查询AI报告解析
     * 
     * @param examCode 检查号
     * @return AI报告解析
     */
    @Override
    public AiReportAnalysis selectAiReportAnalysisByExamCode(String examCode)
    {
        return aiReportAnalysisMapper.selectAiReportAnalysisByExamCode(examCode);
    }

    /**
     * 查询AI报告解析列表
     * 
     * @param aiReportAnalysis AI报告解析
     * @return AI报告解析
     */
    @Override
    public List<AiReportAnalysis> selectAiReportAnalysisList(AiReportAnalysis aiReportAnalysis)
    {
        return aiReportAnalysisMapper.selectAiReportAnalysisList(aiReportAnalysis);
    }

    /**
     * 新增AI报告解析
     * 
     * @param aiReportAnalysis AI报告解析
     * @return 结果
     */
    @Override
    public int insertAiReportAnalysis(AiReportAnalysis aiReportAnalysis)
    {
        aiReportAnalysis.setCreateTime(DateUtils.getNowDate());
        return aiReportAnalysisMapper.insertAiReportAnalysis(aiReportAnalysis);
    }

    /**
     * 修改AI报告解析
     * 
     * @param aiReportAnalysis AI报告解析
     * @return 结果
     */
    @Override
    public int updateAiReportAnalysis(AiReportAnalysis aiReportAnalysis)
    {
        aiReportAnalysis.setUpdateTime(DateUtils.getNowDate());
        return aiReportAnalysisMapper.updateAiReportAnalysis(aiReportAnalysis);
    }

    /**
     * 批量删除AI报告解析
     * 
     * @param ids 需要删除的AI报告解析主键
     * @return 结果
     */
    @Override
    public int deleteAiReportAnalysisByIds(Long[] ids)
    {
        return aiReportAnalysisMapper.deleteAiReportAnalysisByIds(ids);
    }

    /**
     * 删除AI报告解析信息
     * 
     * @param id AI报告解析主键
     * @return 结果
     */
    @Override
    public int deleteAiReportAnalysisById(Long id)
    {
        return aiReportAnalysisMapper.deleteAiReportAnalysisById(id);
    }

    /**
     * 获取或创建AI报告解析（移动端使用）
     * 检查是否已有有效的解析结果，如果没有则创建新的解析任务
     *
     * @param studyId 检查记录ID
     * @return AI报告解析
     */
    @Override
    public AiReportAnalysis getOrCreateAnalysis(Long studyId)
    {
        // 先查询是否已有解析结果
        AiReportAnalysis existingAnalysis = selectAiReportAnalysisByStudyId(studyId);

        // 如果存在且未过期，直接返回
        if (existingAnalysis != null && !isAnalysisExpired(studyId)) {
            return existingAnalysis;
        }

        // 获取检查记录信息
        PacsPatientStudy study = pacsPatientStudyMapper.selectPacsPatientStudyById(studyId);
        if (study == null) {
            throw new RuntimeException("检查记录不存在");
        }

        // 创建新的解析任务
        AiReportAnalysis newAnalysis = new AiReportAnalysis();
        newAnalysis.setStudyId(studyId);
        newAnalysis.setExamCode(study.getExamCode());
        newAnalysis.setAnalysisStatus("pending");
        newAnalysis.setCreateTime(DateUtils.getNowDate());
        newAnalysis.setExpireTime(DateUtils.addDays(DateUtils.getNowDate(), 30)); // 30天过期

        // 如果已存在，更新；否则插入
        if (existingAnalysis != null) {
            newAnalysis.setId(existingAnalysis.getId());
            updateAiReportAnalysis(newAnalysis);
        } else {
            insertAiReportAnalysis(newAnalysis);
        }

        return newAnalysis;
    }

    /**
     * 触发AI报告解析（流式响应）
     *
     * @param studyId 检查记录ID
     * @param sseEmitter SSE发射器
     * @return 解析任务
     */
    @Override
    public AiReportAnalysis triggerAnalysisStream(Long studyId, SseEmitter sseEmitter)
    {
        // 获取或创建解析任务
        AiReportAnalysis analysis = getOrCreateAnalysis(studyId);

        // 获取检查记录内容
        PacsPatientStudy study = pacsPatientStudyMapper.selectPacsPatientStudyById(studyId);
        if (study == null) {
            throw new RuntimeException("检查记录不存在");
        }

        // 构建报告内容（基于pacs_patient_study表的字段）
        StringBuilder reportContent = new StringBuilder();
        if (study.getSee() != null && !study.getSee().trim().isEmpty()) {
            reportContent.append("影像所见：\n").append(study.getSee()).append("\n\n");
        }
        if (study.getReportDiagnose() != null && !study.getReportDiagnose().trim().isEmpty()) {
            reportContent.append("影像意见：\n").append(study.getReportDiagnose());
        }

        if (reportContent.length() == 0) {
            throw new RuntimeException("影像报告内容为空，无法进行AI分析");
        }

        // 更新状态为处理中
        analysis.setAnalysisStatus("processing");
        analysis.setUpdateTime(DateUtils.getNowDate());
        updateAiReportAnalysis(analysis);

        // 异步调用百炼API进行分析
        CompletableFuture<String> future = bailianApiService.analyzeReportStream(reportContent.toString(), sseEmitter);

        future.whenComplete((result, throwable) -> {
            if (throwable != null) {
                // 处理失败
                log.error("AI报告解析失败", throwable);
                analysis.setAnalysisStatus("failed");
                analysis.setErrorMessage(throwable.getMessage());
                analysis.setUpdateTime(DateUtils.getNowDate());
                updateAiReportAnalysis(analysis);
            } else {
                // 处理成功
                analysis.setAnalysisStatus("completed");
                analysis.setAnalysisContent(result);
                analysis.setAnalysisTime(DateUtils.getNowDate());
                analysis.setUpdateTime(DateUtils.getNowDate());
                analysis.setErrorMessage(null);
                updateAiReportAnalysis(analysis);
            }
        });

        return analysis;
    }

    /**
     * 检查解析结果是否过期
     * 判断检查记录的更新时间是否大于AI解析的创建时间
     *
     * @param studyId 检查记录ID
     * @return 是否过期
     */
    @Override
    public boolean isAnalysisExpired(Long studyId)
    {
        AiReportAnalysis analysis = selectAiReportAnalysisByStudyId(studyId);
        if (analysis == null) {
            return true; // 没有解析记录，视为过期
        }

        PacsPatientStudy study = pacsPatientStudyMapper.selectPacsPatientStudyById(studyId);
        if (study == null) {
            return true; // 检查记录不存在，视为过期
        }

        // 检查检查记录更新时间是否大于AI解析创建时间
        Date studyUpdateTime = study.getUpdateTime();
        Date analysisCreateTime = analysis.getCreateTime();

        if (studyUpdateTime != null && analysisCreateTime != null) {
            return studyUpdateTime.after(analysisCreateTime);
        }

        // 检查是否超过过期时间
        if (analysis.getExpireTime() != null) {
            return new Date().after(analysis.getExpireTime());
        }

        return false;
    }

    /**
     * 检查是否有有效的百炼API配置
     *
     * @return 是否有有效配置
     */
    @Override
    public boolean hasValidConfig()
    {
        try {
            return bailianApiService != null;
        } catch (Exception e) {
            log.error("检查百炼API配置时发生错误", e);
            return false;
        }
    }
}
