package com.ruoyi.datasync.service;

/**
 * PACS数据同步服务接口
 */
public interface PacsDataSyncService {
    
    /**
     * 同步检查数据
     * 
     * @return 同步的记录数
     */
    int syncPacsPatientStudy();
    
    /**
     * 同步报告数据
     * 
     * @return 同步的记录数
     */
    int syncPacsReportData();
    
    /**
     * 同步所有PACS数据
     * 
     * @return 是否成功
     */
    boolean syncAllPacsData();
    
    /**
     * 获取最后同步状态
     * 
     * @return 同步状态信息
     */
    String getSyncStatus();
}
