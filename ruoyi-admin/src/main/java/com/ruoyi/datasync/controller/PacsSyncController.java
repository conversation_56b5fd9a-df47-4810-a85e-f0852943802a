package com.ruoyi.datasync.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.datasync.domain.PacsSyncStatus;
import com.ruoyi.datasync.mapper.PacsSyncStatusMapper;
import com.ruoyi.datasync.service.PacsDataSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * PACS数据同步Controller
 */
@RestController
@RequestMapping("/datasync/pacs")
public class PacsSyncController extends BaseController {

    @Autowired
    private PacsDataSyncService pacsDataSyncService;
    
    @Autowired
    private PacsSyncStatusMapper pacsSyncStatusMapper;

    /**
     * 同步检查数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS检查数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncCloud")
    public AjaxResult syncPacsCloudData() {
        int count = pacsDataSyncService.syncPacsPatientStudy();
        return AjaxResult.success("同步成功，共同步 " + count + " 条记录");
    }

    /**
     * 同步报告数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS报告数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncReport")
    public AjaxResult syncPacsReportData() {
        int count = pacsDataSyncService.syncPacsReportData();
        return AjaxResult.success("同步成功，共同步 " + count + " 条记录");
    }

    /**
     * 同步所有数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:sync')")
    @Log(title = "PACS全量数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/syncAll")
    public AjaxResult syncAllPacsData() {
        boolean success = pacsDataSyncService.syncAllPacsData();
        if (success) {
            return AjaxResult.success("所有数据同步成功");
        } else {
            return AjaxResult.error("数据同步失败，请查看日志");
        }
    }

    /**
     * 获取同步状态
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:query')")
    @GetMapping("/status")
    public AjaxResult getSyncStatus() {
        String status = pacsDataSyncService.getSyncStatus();
        return AjaxResult.success("查询成功", status);
    }
    
    /**
     * 获取同步统计数据
     */
    @PreAuthorize("@ss.hasPermi('datasync:pacs:query')")
    @GetMapping("/statistics")
    public AjaxResult getSyncStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取检查数据同步状态
        PacsSyncStatus studyStatus = pacsSyncStatusMapper.selectByType("pacs_study");
        Map<String, Object> studyData = new HashMap<>();
        
        if (studyStatus != null) {
            studyData.put("status", studyStatus.getSyncStatus());
            studyData.put("insertCount", studyStatus.getInsertCount());
            studyData.put("updateCount", studyStatus.getUpdateCount());
            studyData.put("totalCount", studyStatus.getInsertCount() + studyStatus.getUpdateCount());
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            studyData.put("lastSyncTime", studyStatus.getLastSyncTime() != null ? 
                    sdf.format(studyStatus.getLastSyncTime()) : "未同步");
            studyData.put("remark", studyStatus.getRemark());
        } else {
            studyData.put("status", "未同步");
            studyData.put("insertCount", 0);
            studyData.put("updateCount", 0);
            studyData.put("totalCount", 0);
            studyData.put("lastSyncTime", "未同步");
            studyData.put("remark", "");
        }
        
        // 获取报告数据同步状态
        PacsSyncStatus reportStatus = pacsSyncStatusMapper.selectByType("pacs_report");
        Map<String, Object> reportData = new HashMap<>();
        
        if (reportStatus != null) {
            reportData.put("status", reportStatus.getSyncStatus());
            reportData.put("insertCount", reportStatus.getInsertCount());
            reportData.put("updateCount", reportStatus.getUpdateCount());
            reportData.put("totalCount", reportStatus.getInsertCount() + reportStatus.getUpdateCount());
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            reportData.put("lastSyncTime", reportStatus.getLastSyncTime() != null ? 
                    sdf.format(reportStatus.getLastSyncTime()) : "未同步");
            reportData.put("remark", reportStatus.getRemark());
        } else {
            reportData.put("status", "未同步");
            reportData.put("insertCount", 0);
            reportData.put("updateCount", 0);
            reportData.put("totalCount", 0);
            reportData.put("lastSyncTime", "未同步");
            reportData.put("remark", "");
        }
        
        result.put("study", studyData);
        result.put("report", reportData);
        
        return AjaxResult.success(result);
    }
} 