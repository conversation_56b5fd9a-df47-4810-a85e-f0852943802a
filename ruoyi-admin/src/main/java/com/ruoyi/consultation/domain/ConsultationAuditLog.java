package com.ruoyi.consultation.domain;

import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 会诊操作日志对象 consultation_audit_log
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
public class ConsultationAuditLog {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 会诊申请ID */
    private Long consultationId;

    /** 操作类型 */
    @Excel(name = "操作类型", readConverterExp = "CREATE=创建,ACCEPT=接受,REJECT=拒绝,COMPLETE=完成,CANCEL=取消,UPDATE=更新")
    private String operationType;

    /** 操作用户ID */
    private Long operationUserId;

    /** 操作用户姓名 */
    @Excel(name = "操作用户")
    private String operationUserName;

    /** 操作时间 */
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    /** 操作IP */
    @Excel(name = "操作IP")
    private String operationIp;

    /** 操作描述 */
    @Excel(name = "操作描述")
    private String operationDescription;

    /** 原状态 */
    @Excel(name = "原状态")
    private String oldStatus;

    /** 新状态 */
    @Excel(name = "新状态")
    private String newStatus;

    /** 操作内容（JSON格式） */
    private String operationContent;

    /** 备注 */
    private String remark;

    /**
     * 获取操作类型显示文本
     */
    public String getOperationTypeText() {
        switch (operationType) {
            case "CREATE":
                return "创建";
            case "ACCEPT":
                return "接受";
            case "REJECT":
                return "拒绝";
            case "COMPLETE":
                return "完成";
            case "CANCEL":
                return "取消";
            case "UPDATE":
                return "更新";
            default:
                return operationType;
        }
    }
}
