/**
 * AI报告解析相关API
 */
import http from './http'
const { get, post } = http

/**
 * 获取AI报告解析结果
 * @param {number} studyId - 检查记录ID
 * @returns {Promise} 响应结果
 */
export function getAiAnalysis(studyId) {
  return get(`/mobile/ai/report/analysis/${studyId}`)
}

/**
 * 获取AI报告解析状态
 * @param {number} studyId - 检查记录ID
 * @returns {Promise} 响应结果
 */
export function getAnalysisStatus(studyId) {
  return get(`/mobile/ai/report/analysis/status/${studyId}`)
}

/**
 * 重新分析报告
 * @param {number} studyId - 检查记录ID
 * @returns {Promise} 响应结果
 */
export function retryAnalysis(studyId) {
  return post(`/mobile/ai/report/analysis/retry/${studyId}`)
}

/**
 * 创建SSE连接进行流式AI分析
 * @param {number} studyId - 检查记录ID
 * @param {Object} callbacks - 回调函数对象
 * @param {Function} callbacks.onStart - 开始回调
 * @param {Function} callbacks.onData - 数据回调
 * @param {Function} callbacks.onComplete - 完成回调
 * @param {Function} callbacks.onError - 错误回调
 * @returns {EventSource} EventSource对象
 */
export function createAnalysisStream(studyId, callbacks = {}) {
  const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'
  const url = `${baseURL}/mobile/ai/report/analysis/stream/${studyId}`

  const eventSource = new EventSource(url)

  // 通用消息事件（用于接收所有类型的消息）
  eventSource.onmessage = (event) => {
    console.log('接收到SSE通用消息:', event)
    // 如果没有指定事件类型，默认作为数据处理
    if (callbacks.onData) {
      callbacks.onData(event.data)
    }
  }

  // 开始事件
  eventSource.addEventListener('start', (event) => {
    console.log('接收到SSE开始事件:', event)
    if (callbacks.onStart) {
      callbacks.onStart(event.data)
    }
  })

  // 数据事件
  eventSource.addEventListener('data', (event) => {
    console.log('接收到SSE数据事件:', event.data)
    if (callbacks.onData) {
      callbacks.onData(event.data)
    }
  })

  // 完成事件
  eventSource.addEventListener('complete', (event) => {
    console.log('接收到SSE完成事件:', event)
    if (callbacks.onComplete) {
      callbacks.onComplete(event.data)
    }
    eventSource.close()
  })

  // 错误事件
  eventSource.addEventListener('error', (event) => {
    console.log('接收到SSE错误事件:', event)
    if (callbacks.onError) {
      callbacks.onError(event.data || 'AI分析发生错误')
    }
    eventSource.close()
  })

  // 连接错误处理
  eventSource.onerror = (error) => {
    console.error('SSE连接错误:', error)
    console.log('SSE连接状态:', eventSource.readyState)

    // 检查连接状态
    if (eventSource.readyState === EventSource.CONNECTING) {
      console.log('SSE正在重连...')
    } else if (eventSource.readyState === EventSource.CLOSED) {
      console.log('SSE连接已关闭')
      if (callbacks.onError) {
        callbacks.onError('连接已断开')
      }
    } else {
      console.log('SSE连接失败')
      if (callbacks.onError) {
        callbacks.onError('连接服务器失败，请检查网络')
      }
    }
  }

  // 连接打开事件
  eventSource.onopen = (event) => {
    console.log('SSE连接已建立:', event)
  }

  return eventSource
}

/**
 * 创建SSE诊断连接（用于生产环境问题排查）
 * @param {Object} callbacks - 回调函数对象
 * @returns {EventSource} EventSource对象
 */
export function createDiagnosisStream(callbacks = {}) {
  const baseURL = import.meta.env.VITE_API_BASE_URL || '/api'
  const url = `${baseURL}/mobile/ai/report/diagnosis/sse`

  console.log('创建SSE诊断连接:', url)
  const eventSource = new EventSource(url)

  // 通用消息事件
  eventSource.onmessage = (event) => {
    console.log('诊断-接收到SSE通用消息:', event)
    if (callbacks.onData) {
      callbacks.onData(event.data)
    }
  }

  // 开始事件
  eventSource.addEventListener('start', (event) => {
    console.log('诊断-接收到SSE开始事件:', event)
    if (callbacks.onStart) {
      callbacks.onStart(event.data)
    }
  })

  // 数据事件
  eventSource.addEventListener('data', (event) => {
    console.log('诊断-接收到SSE数据事件:', event.data)
    if (callbacks.onData) {
      callbacks.onData(event.data)
    }
  })

  // 完成事件
  eventSource.addEventListener('complete', (event) => {
    console.log('诊断-接收到SSE完成事件:', event)
    if (callbacks.onComplete) {
      callbacks.onComplete(event.data)
    }
    eventSource.close()
  })

  // 错误事件
  eventSource.addEventListener('error', (event) => {
    console.log('诊断-接收到SSE错误事件:', event)
    if (callbacks.onError) {
      callbacks.onError(event.data || 'SSE诊断发生错误')
    }
    eventSource.close()
  })

  // 连接错误处理
  eventSource.onerror = (error) => {
    console.error('诊断-SSE连接错误:', error)
    console.log('诊断-SSE连接状态:', eventSource.readyState)

    if (eventSource.readyState === EventSource.CONNECTING) {
      console.log('诊断-SSE正在重连...')
    } else if (eventSource.readyState === EventSource.CLOSED) {
      console.log('诊断-SSE连接已关闭')
      if (callbacks.onError) {
        callbacks.onError('诊断连接已断开')
      }
    } else {
      console.log('诊断-SSE连接失败')
      if (callbacks.onError) {
        callbacks.onError('诊断连接服务器失败，请检查网络')
      }
    }
  }

  // 连接打开事件
  eventSource.onopen = (event) => {
    console.log('诊断-SSE连接已建立:', event)
  }

  return eventSource
}

export default {
  getAiAnalysis,
  getAnalysisStatus,
  retryAnalysis,
  createAnalysisStream,
  createDiagnosisStream
}
