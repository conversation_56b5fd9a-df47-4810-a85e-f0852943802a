<template>
  <div class="ai-analysis">
    <van-nav-bar
        title="AI报告解析"
        left-text="返回"
        @click-left="goBack"
        fixed
    />

    <div class="content-container">
      <!-- 免责声明 -->
      <div class="disclaimer-card">
        <div class="disclaimer-header">
          <van-icon name="warning-o" color="#ff6b35" />
          <span class="disclaimer-title">重要提示</span>
        </div>
        <div class="disclaimer-content">
          <p>• AI报告解析仅供参考，不能替代医生的专业诊断</p>
          <p>• 请以主治医生的诊断意见为准</p>
          <p>• 如有疑问，请及时咨询专业医生</p>
        </div>
      </div>

      <!-- 分析状态卡片 -->
      <div class="status-card" v-if="showStatus">
        <div class="status-header">
          <van-icon :name="statusIcon" :color="statusColor" />
          <span class="status-text">{{ statusText }}</span>
        </div>
        <div class="status-actions" v-if="analysisStatus === 'failed' || analysisStatus === 'not_found'">
          <van-button 
            type="primary" 
            size="small" 
            @click="startAnalysis"
            :loading="isAnalyzing"
          >
            {{ analysisStatus === 'failed' ? '重新分析' : '开始分析' }}
          </van-button>
        </div>
      </div>

      <!-- 分析结果 -->
      <div class="analysis-result" v-if="analysisContent">
        <div class="result-header">
          <van-icon name="bulb-o" color="#1989fa" />
          <span class="result-title">AI解析结果</span>
        </div>
        <div class="result-content" v-html="formattedContent"></div>
        <div class="result-footer">
          <span class="analysis-time">解析时间：{{ formatTime(analysisTime) }}</span>
        </div>
      </div>

      <!-- 实时分析输出 -->
      <div class="streaming-output" v-if="isStreaming">
        <div class="streaming-header">
          <van-loading size="16px" color="#1989fa" />
          <span class="streaming-title">AI正在分析中...</span>
        </div>
        <div class="streaming-content" ref="streamingContent" v-html="formattedStreamingContent"></div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="analysisContent && !isStreaming">
        <van-button 
          type="default" 
          size="large" 
          @click="startAnalysis"
          :loading="isAnalyzing"
          block
        >
          重新分析
        </van-button>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!analysisContent && !isStreaming && !showStatus">
        <van-empty 
          image="search" 
          description="暂无AI解析结果"
        >
          <van-button 
            type="primary" 
            size="small" 
            @click="startAnalysis"
            :loading="isAnalyzing"
          >
            开始AI分析
          </van-button>
        </van-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Toast } from 'vant'
import { marked } from 'marked'
import { getAiAnalysis, getAnalysisStatus, createAnalysisStream } from '../api/aiAnalysis'

const route = useRoute()
const router = useRouter()

// 响应式数据
const analysisContent = ref('')
const analysisStatus = ref('')
const analysisTime = ref('')
const errorMessage = ref('')
const isAnalyzing = ref(false)
const isStreaming = ref(false)
const streamingText = ref('')
const showStatus = ref(false)
const streamingContent = ref(null)

// 计算属性
const statusIcon = computed(() => {
  switch (analysisStatus.value) {
    case 'pending': return 'clock-o'
    case 'processing': return 'loading'
    case 'completed': return 'success'
    case 'failed': return 'cross'
    default: return 'info-o'
  }
})

const statusColor = computed(() => {
  switch (analysisStatus.value) {
    case 'pending': return '#ff9500'
    case 'processing': return '#1989fa'
    case 'completed': return '#07c160'
    case 'failed': return '#ee0a24'
    default: return '#969799'
  }
})

const statusText = computed(() => {
  switch (analysisStatus.value) {
    case 'pending': return '等待分析'
    case 'processing': return '分析中...'
    case 'completed': return '分析完成'
    case 'failed': return `分析失败${errorMessage.value ? '：' + errorMessage.value : ''}`
    case 'not_found': return '暂无分析记录'
    default: return '未知状态'
  }
})

// 配置marked选项，适配移动端
marked.setOptions({
  breaks: true, // 支持换行
  gfm: true, // 支持GitHub风格的Markdown
  headerIds: false, // 禁用标题ID
  mangle: false // 禁用标题混淆
})

const formattedContent = computed(() => {
  if (!analysisContent.value) return ''

  try {
    // 使用marked库解析Markdown格式
    let content = analysisContent.value
    // 预处理：确保换行符正确
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
    return marked.parse(content)
  } catch (error) {
    console.error('Markdown解析失败:', error)
    // 降级处理：简单的文本格式化
    return analysisContent.value
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
  }
})

const formattedStreamingContent = computed(() => {
  if (!streamingText.value) return ''

  try {
    // 实时流式内容也使用Markdown解析
    let content = streamingText.value
    // 预处理：确保换行符正确
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
    return marked.parse(content)
  } catch (error) {
    console.error('流式内容Markdown解析失败:', error)
    // 降级处理：简单的文本格式化
    return streamingText.value
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
  }
})

// 方法
function goBack() {
  router.go(-1)
}

function formatTime(time) {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN')
}

async function loadAnalysis() {
  try {
    const studyId = route.params.studyId
    const response = await getAiAnalysis(studyId)
    
    if (response.data.code === 200 && response.data.data) {
      const data = response.data.data
      analysisContent.value = data.analysisContent || ''
      analysisStatus.value = data.analysisStatus || ''
      analysisTime.value = data.analysisTime || ''
      errorMessage.value = data.errorMessage || ''
      showStatus.value = false
    } else {
      // 没有分析结果，检查状态
      await checkAnalysisStatus()
    }
  } catch (error) {
    console.error('加载AI分析失败:', error)
    Toast.fail('加载AI分析失败')
  }
}

async function checkAnalysisStatus() {
  try {
    const studyId = route.params.studyId
    const response = await getAnalysisStatus(studyId)
    
    if (response.data.code === 200) {
      const data = response.data.data
      analysisStatus.value = data === 'not_found' ? 'not_found' : data.status
      errorMessage.value = data.errorMessage || ''
      showStatus.value = true
      
      if (data.isExpired) {
        analysisStatus.value = 'expired'
        showStatus.value = true
      }
    }
  } catch (error) {
    console.error('检查分析状态失败:', error)
    analysisStatus.value = 'not_found'
    showStatus.value = true
  }
}

function startAnalysis() {
  if (isAnalyzing.value || isStreaming.value) return

  const studyId = route.params.studyId
  isAnalyzing.value = true
  isStreaming.value = true
  streamingText.value = ''
  analysisContent.value = ''
  showStatus.value = false

  const eventSource = createAnalysisStream(studyId, {
    onStart: (data) => {
      Toast.success('开始AI分析')
      streamingText.value = '正在连接AI服务...\n'
    },
    onData: (data) => {
      // 确保数据不为空
      if (data && data.trim()) {
        streamingText.value += data
        // 自动滚动到底部
        nextTick(() => {
          if (streamingContent.value) {
            streamingContent.value.scrollTop = streamingContent.value.scrollHeight
          }
        })
      }
    },
    onComplete: (data) => {
      isAnalyzing.value = false
      isStreaming.value = false
      Toast.success('AI分析完成')
      // 重新加载分析结果
      setTimeout(() => {
        loadAnalysis()
      }, 1000)
    },
    onError: (error) => {
      isAnalyzing.value = false
      isStreaming.value = false
      Toast.fail('AI分析失败：' + error)
      analysisStatus.value = 'failed'
      errorMessage.value = error
      showStatus.value = true
    }
  })

  // 保存eventSource引用以便清理
  window.currentEventSource = eventSource
}

onMounted(() => {
  loadAnalysis()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (window.currentEventSource) {
    window.currentEventSource.close()
    window.currentEventSource = null
  }
})
</script>

<style scoped>
.ai-analysis {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 46px;
}

.content-container {
  padding: 16px;
  padding-bottom: 80px;
}

/* 免责声明卡片 */
.disclaimer-card {
  background: linear-gradient(135deg, #fff5f5 0%, #fff0f0 100%);
  border: 1px solid #ffebee;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
}

.disclaimer-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.disclaimer-title {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #ff6b35;
}

.disclaimer-content {
  color: #666;
  line-height: 1.6;
}

.disclaimer-content p {
  margin: 4px 0;
  font-size: 14px;
}

/* 状态卡片 */
.status-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.status-text {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 500;
}

.status-actions {
  text-align: center;
}

/* 分析结果 */
.analysis-result {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.result-title {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1989fa;
}

.result-content {
  color: #333;
  line-height: 1.8;
  font-size: 14px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Markdown样式优化 - 移动端适配 */
.result-content :deep(h1),
.result-content :deep(h2),
.result-content :deep(h3),
.result-content :deep(h4),
.result-content :deep(h5),
.result-content :deep(h6) {
  color: #1989fa;
  font-weight: 600;
  margin: 16px 0 8px 0;
  padding-left: 8px;
  border-left: 3px solid #1989fa;
  line-height: 1.4;
}

.result-content :deep(h1) { font-size: 18px; }
.result-content :deep(h2) { font-size: 17px; }
.result-content :deep(h3) { font-size: 16px; }
.result-content :deep(h4) { font-size: 15px; }
.result-content :deep(h5) { font-size: 14px; }
.result-content :deep(h6) { font-size: 14px; }

.result-content :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
}

.result-content :deep(ul),
.result-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.result-content :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.result-content :deep(ul li) {
  list-style-type: disc;
}

.result-content :deep(ol li) {
  list-style-type: decimal;
}

.result-content :deep(strong) {
  color: #1989fa;
  font-weight: 600;
}

.result-content :deep(em) {
  color: #666;
  font-style: italic;
}

.result-content :deep(code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.result-content :deep(pre) {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.result-content :deep(blockquote) {
  border-left: 4px solid #1989fa;
  padding-left: 12px;
  margin: 8px 0;
  color: #666;
  font-style: italic;
}

.result-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.analysis-time {
  font-size: 12px;
  color: #999;
}

/* 流式输出 */
.streaming-output {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.streaming-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.streaming-title {
  margin-left: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #1989fa;
}

.streaming-content {
  max-height: 400px;
  overflow-y: auto;
  color: #333;
  line-height: 1.6;
  font-size: 14px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 流式内容的Markdown样式 */
.streaming-content :deep(h1),
.streaming-content :deep(h2),
.streaming-content :deep(h3),
.streaming-content :deep(h4),
.streaming-content :deep(h5),
.streaming-content :deep(h6) {
  color: #1989fa;
  font-weight: 600;
  margin: 12px 0 6px 0;
  line-height: 1.4;
}

.streaming-content :deep(h1) { font-size: 17px; }
.streaming-content :deep(h2) { font-size: 16px; }
.streaming-content :deep(h3) { font-size: 15px; }
.streaming-content :deep(h4) { font-size: 14px; }
.streaming-content :deep(h5) { font-size: 13px; }
.streaming-content :deep(h6) { font-size: 13px; }

.streaming-content :deep(p) {
  margin: 6px 0;
  line-height: 1.5;
}

.streaming-content :deep(ul),
.streaming-content :deep(ol) {
  margin: 6px 0;
  padding-left: 18px;
}

.streaming-content :deep(li) {
  margin: 2px 0;
  line-height: 1.5;
}

.streaming-content :deep(strong) {
  color: #1989fa;
  font-weight: 600;
}

.streaming-content :deep(em) {
  color: #666;
  font-style: italic;
}



/* 操作按钮 */
.action-buttons {
  margin-top: 16px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .content-container {
    padding: 12px;
  }

  .disclaimer-card,
  .status-card,
  .analysis-result,
  .streaming-output {
    padding: 12px;
  }
}
</style>
