<template>
  <div class="dicom-request">
    <van-nav-bar
      title="申请影像数据"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
    />
    
    <div class="content-container">
      <van-form @submit="onSubmit">
        <!-- 展示报告基本信息 -->
        <van-cell-group inset title="检查信息">
          <van-cell title="患者姓名" :value="reportInfo.patientName" />
          <van-cell title="检查类型" :value="reportInfo.modality" />
          <van-cell title="检查部位" :value="reportInfo.organ" />
          <van-cell title="检查日期" :value="reportInfo.registerTime" />
          <van-cell title="检查科室" :value="reportInfo.examDepartment" />
          <van-cell title="检查医生" :value="reportInfo.examDoctorName" />
          <van-cell title="临床诊断" :value="reportInfo.clinicalDiagnosis" />
        </van-cell-group>

        <!-- 申请表单 -->
        <van-cell-group inset title="申请信息">
          <van-field
            v-model="formData.phone"
            name="phone"
            label="手机号码"
            placeholder="请输入接收通知的手机号码"
            :rules="[{ required: true, message: '请填写手机号码' }]"
          />
          <van-field
            v-model="formData.reason"
            name="reason"
            label="申请原因"
            type="textarea"
            rows="2"
            placeholder="请简要说明申请原因"
            :rules="[{ required: false, message: '请填写申请原因' }]"
          />
        </van-cell-group>

        <van-notice-bar
          left-icon="info-o"
          text="影像数据处理预计需要5分钟，请稍后重新进入页面查看。数据将为您保留90天，过期后需重新申请。"
          wrapable
          :scrollable="false"
          style="margin: 16px 16px 0;"
        />

        <div style="margin: 16px">
          <van-button round block type="primary" native-type="submit">
            提交申请
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Toast, Dialog } from 'vant'
import { requestDicom, getReportDetail } from '../api/report'

const route = useRoute()
const router = useRouter()

const reportInfo = ref({})
const formData = ref({
  phone: '',
  reason: ''
})
const submitting = ref(false)

// 获取报告详情
async function fetchReportDetail() {
  try {
    const res = await getReportDetail(route.params.id)
    //console.log(res)
    if (res.status === 200 && res.data) {
      reportInfo.value = res.data.data
      // 如果有手机号，自动填充
      if (res.data.data.mobile) {
        formData.value.phone = res.data.data.mobile
      }
    } else {
      Toast.fail(res.msg || '获取报告信息失败')
    }
  } catch (error) {
    console.error('获取报告信息失败:', error)
    Toast.fail('获取报告信息失败')
  }
}

// 提交申请
async function onSubmit() {
  // 防止重复提交
  if (submitting.value) {
    return
  }
  
  submitting.value = true
  Toast.loading({
    message: '提交中...',
    forbidClick: true,
    duration: 0
  })
  
  try {
    const params = {
      reportId: route.params.id,
      ...formData.value,
      patientName: reportInfo.value.patientName,
      idNo: reportInfo.value.idNo,
      originPatientId: reportInfo.value.originalPatientId,
      status: '待同步',
      examCode: reportInfo.value.examCode,
    }
    
    const res = await requestDicom(params)
    console.log(res)
    if (res.status === 200) {
      Toast.clear() // 清除加载提示
      // 使用Dialog替代Toast，提供更强烈的视觉提示
      Dialog.alert({
        title: '申请成功',
        message: '您的影像数据申请已提交成功，数据处理需要约5分钟，请稍后重新进入页面查看。数据将为您保留90天，过期后需重新申请。',
        theme: 'round-button',
        confirmButtonText: '我知道了',
        confirmButtonColor: '#07c160',
      }).then(() => {
        router.back()
      })
    } else {
      Toast.clear()
      Dialog.alert({
        title: '申请失败',
        message: res.msg || '申请提交失败，请稍后重试',
        theme: 'round-button',
      })
    }
  } catch (error) {
    console.error('申请提交失败:', error)
    Toast.clear()
    Dialog.alert({
      title: '申请失败',
      message: '网络异常，请稍后重试',
      theme: 'round-button',
    })
  } finally {
    submitting.value = false
  }
}

function goBack() {
  router.back()
}

onMounted(() => {
  fetchReportDetail()
})
</script>

<style scoped>
.dicom-request {
  min-height: 100vh;
  padding-top: 46px;
  background-color: #f7f8fa;
}

.content-container {
  padding: 16px;
}
</style> 