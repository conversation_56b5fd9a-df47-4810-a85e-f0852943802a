<template>
	<div class="login-container">
		<div class="decorative-bg">
			<div class="hospital-pattern"></div>
		</div>
		
		<div class="hospital-logo">
			<!-- <img :src="logo" class="logo-image" /> -->
		</div>
		
		<div class="login-content">
			<div class="title-section">
				<span class="hospital-name">{{ loginConfig.hospitalName }}</span>
				<span class="system-name">{{ loginConfig.systemName }}</span>
			</div>

			<span class="welcome-text">{{ loginConfig.welcomeText }}</span>
			<span class="sub-title">{{ loginConfig.subTitle }}</span>
			
			<label class="input-label">手机号</label>
			<input
				class="login-input"
				v-model="loginForm.phone"
				type="text"
				maxlength="11"
				placeholder="请输入检查时留的手机号"
				@blur="validatePhone(loginForm.phone)"
			/>
			
			<div class="sms-code-group">
				<label class="input-label">验证码</label>
				<div class="input-wrapper">
					<input
						class="login-input sms-input"
						v-model="loginForm.smsCode"
						type="text"
						maxlength="6"
						placeholder="请输入6位验证码"
					/>
					<button 
						class="sms-button"
						:disabled="smsBtnDisabled"
						@click="sendSmsCode"
					>
						{{ smsBtnTip }}
					</button>
				</div>
			</div>

			<button 
				class="login-button"
				:class="{ 'loading': isSubmitting }"
				:disabled="!formValid || isSubmitting"
				@click="handleSubmit"
			>
				登录
			</button>
			
			<div class="service-info">
				<span class="service-text">{{ loginConfig.footerText }}</span>
			</div>
		</div>
	</div>
</template>

<script>
	import { useUserInfoStore } from '@/stores/userInfo'
	import { Toast } from 'vant'
	import logo from '@/assets/logo.png'
	import http from '@/api/http'
	import { authStorage, uiStorage } from '@/utils/storage'
	import { getLoginConfig } from '@/api/config'

	export default {
		setup() {
			const userInfoStore = useUserInfoStore()
			return {
				userInfoStore
			}
		},
		data() {
			return {
				isSubmitting: false,
				smsBtnDisabled: false,
				smsBtnTip: '获取验证码',
				loginForm: {
					phone: '',
					smsCode: ''
				},
				countdown: 60,
				timer: null,
				logo: logo,
				loginConfig: {
					hospitalName: '医院名称',
					systemName: '云影像服务平台',
					footerText: '本系统提供检查报告查询、影像查看等服务',
					welcomeText: '欢迎使用',
					subTitle: '请使用预留手机号登录，查看您的检查报告和影像'
				}
			}
		},
		mounted() {
			this.checkCountdown();
			this.loadLoginConfig();
		},
		beforeUnmount() {
			if (this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			}
		},
		computed: {
			formValid() {
				return this.loginForm.phone?.length === 11 && 
					   this.loginForm.smsCode?.length === 6
			}
		},
		methods: {
			async loadLoginConfig() {
				try {
					const response = await getLoginConfig();
					if (response.data.code === 200 && response.data.data) {
						this.loginConfig = { ...this.loginConfig, ...response.data.data };
					}
				} catch (error) {
					console.error('加载登录配置失败:', error);
					// 使用默认配置，不显示错误提示
				}
			},

			async checkCountdown() {
				try {
					const endTime = await uiStorage.getItem('smsCountdownEndTime');
					if (endTime) {
						const now = Date.now();
						const end = parseInt(endTime);
						const remaining = Math.ceil((end - now) / 1000);
						
						if (remaining > 0) {
							this.countdown = remaining;
							this.smsBtnDisabled = true;
							this.smsBtnTip = `${this.countdown}秒后重试`;
							this.startCountdown();
						} else {
							await uiStorage.removeItem('smsCountdownEndTime');
						}
					}
				} catch (err) {
					console.error('验证码计时器读取失败:', err);
				}
			},

			async startCountdown() {
				this.smsBtnDisabled = true;
				this.countdown = 60;
				this.smsBtnTip = `${this.countdown}秒后重试`;
				
				const endTime = Date.now() + this.countdown * 1000;
				try {
					await uiStorage.setItem('smsCountdownEndTime', endTime.toString());
				} catch (err) {
					console.error('验证码计时器保存失败:', err);
				}
				
				if (this.timer) {
					clearInterval(this.timer);
				}
				
				this.timer = setInterval(() => {
					if (this.countdown > 0) {
						this.countdown--;
						this.smsBtnTip = `${this.countdown}秒后重试`;
					} else {
						this.stopCountdown();
					}
				}, 1000);
			},

			async stopCountdown() {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
				this.smsBtnDisabled = false;
				this.smsBtnTip = '获取验证码';
				try {
					await uiStorage.removeItem('smsCountdownEndTime');
				} catch (err) {
					console.error('验证码计时器清除失败:', err);
				}
			},

			validatePhone(value) {
				if (!value) return '请输入手机号';
				if (!/^1[3-9]\d{9}$/.test(value)) {
					Toast('手机号格式不正确');
					return false;
				}
				return true;
			},

			sendSmsCode() {
				if (!this.loginForm.phone) {
					Toast('请填写手机号');
					return;
				}
				
				if (!this.validatePhone(this.loginForm.phone)) {
					return;
				}
				
				if (!this.smsBtnDisabled) {
					Toast.loading({
						message: '发送中...',
						forbidClick: true,
						duration: 0
					});
					http.post('/mobile/sendSmsCode', {
						phone: this.loginForm.phone,
						operationType: 'login'
					}).then(response => {
						Toast.clear();
						if (response.data.code === 200) {
							this.startCountdown();
							Toast.success('验证码发送成功');
						} else {
							Toast.fail(response.data.msg);
						}
					}).catch(error => {
						Toast.clear();
						Toast.fail('发送失败，请重试');
					});
				}
			},

			async handleSubmit() {
				if (this.isSubmitting) return
				this.isSubmitting = true
				
				try {
					const phone = (this.loginForm.phone || '').toString().trim()
					const smsCode = (this.loginForm.smsCode || '').toString().trim()
					
					if (!phone || !smsCode) {
						Toast('请填写手机号和验证码');
						return;
					}

					const response = await http.post('/mobile/smsLogin', {
						phone: phone,
						smsCode: smsCode
					});
					
					if (response.data.code === 200) {
						const user = response.data.user;
						const token = user.token;
						
						try {
							// 存储 token 到 authStorage
							await authStorage.setItem('token', token);
							// 同时保留旧的方式，确保过渡兼容
							localStorage.setItem('token', token);
							
							// 设置已经通过query访问，避免登录后还需要query参数
							await authStorage.setItem('hasQueryAccess', true);
						} catch (err) {
							console.error('登录令牌保存失败:', err);
							// 如果保存失败，确保至少本地存储有值
							localStorage.setItem('token', token);
						}

						this.userInfoStore.setAccount(user);
						
						// 改为使用报告列表路径，同时设置query参数
						this.$router.replace({
							path: '/',
							query: {
								query: phone
							}
						});
					} else {
						Toast.fail(response.data.msg);
					}
				} catch (error) {
					Toast.fail(error.message || '登录失败，请重试');
				} finally {
					this.isSubmitting = false
				}
			}
		}
	}
</script>

<style scoped>
.login-container {
	padding: 20px;
	position: relative;
	min-height: 100vh;
	background: linear-gradient(180deg, #f8fbff 0%, #ffffff 100%);
}

.hospital-logo {
	position: relative;
	z-index: 5;
	text-align: center;
	margin-bottom: 20px;
}

.logo-image {
	width: 80px;
	height: 80px;
	border-radius: 10px;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.title-section {
	text-align: center;
	margin-bottom: 20px;
}

.hospital-name {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 4px;
}

.system-name {
	font-size: 14px;
	color: #666;
	display: block;
}

.welcome-text {
	font-size: 24px;
	font-weight: 500;
	margin-bottom: 8px;
	display: block;
	position: relative;
	z-index: 5;
	color: #333;
}

.sub-title {
	font-size: 14px;
	color: #666;
	margin-bottom: 24px;
	display: block;
	position: relative;
	z-index: 5;
}

.login-content {
	position: relative;
	z-index: 2;
	background: #ffffff;
	border-radius: 10px;
	padding: 20px;
	margin-top: 20px;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	animation: slideUp 0.5s ease-out;
}

.input-label {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 8px;
}

.login-input {
	width: 100%;
	height: 40px;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 0 12px;
	font-size: 14px;
	margin-bottom: 16px;
	transition: all 0.3s ease;
}

.login-input:focus {
	border-color: #2B5BA1;
	box-shadow: 0 0 0 2px rgba(43, 91, 161, 0.1);
	transform: translateY(-2px);
}

.sms-code-group {
	position: relative;
	margin-bottom: 16px;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	gap: 12px;
}

.sms-input {
	margin-bottom: 0;
}

.sms-button {
	flex-shrink: 0;
	width: 100px;
	height: 40px;
	background: #2B5BA1 !important;
	color: #fff !important;
	border-radius: 4px;
	font-size: 14px;
	font-weight: normal;
	line-height: 40px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	transition: all 0.3s ease;
	border: none;
	cursor: pointer;
	padding: 0;
}

.sms-button:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.sms-button[disabled] {
	background: #CCD0D7 !important;
	opacity: 0.8;
	cursor: not-allowed;
}

.login-button {
	width: 100%;
	height: 48px;
	border-radius: 24px;
	background: #2B5BA1;
	color: white;
	font-size: 16px;
	margin-top: 24px;
	transition: all 0.3s;
	line-height: 48px;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
	box-shadow: 0 4px 10px rgba(43, 91, 161, 0.3);
	border: none;
	cursor: pointer;
}

.login-button:active {
	transform: scale(0.98);
	opacity: 0.9;
}

.login-button.loading {
	opacity: 0.7;
	pointer-events: none;
	position: relative;
}

.login-button.loading::after {
	content: '';
	position: absolute;
	width: 20px;
	height: 20px;
	top: 50%;
	left: 50%;
	margin: -10px 0 0 -10px;
	border: 2px solid rgba(255, 255, 255, 0.3);
	border-top-color: #fff;
	border-radius: 50%;
	animation: spin 0.8s linear infinite;
}

.login-button[disabled] {
	background: #CCD0D7;
	pointer-events: none;
	box-shadow: none;
	cursor: not-allowed;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

.service-info {
	margin-top: 24px;
	text-align: center;
}

.service-text {
	font-size: 12px;
	color: #999;
	display: block;
	line-height: 1.6;
}

.decorative-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 190px;
	background: linear-gradient(135deg, #2B5BA1 0%, #1E3F7A 100%);
	border-bottom-left-radius: 50% 40px;
	border-bottom-right-radius: 50% 40px;
	z-index: 1;
	overflow: hidden;
}

.hospital-pattern {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: 
		radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 20%),
		radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 20%);
	opacity: 0.6;
}

@keyframes slideUp {
	from {
		transform: translateY(15px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
</style>