import localforage from 'localforage';

// 配置 localforage
localforage.config({
  name: 'pacs-mobile',
  storeName: 'app_storage',
  description: '医院影像系统数据存储'
});

// 创建专门的实例以区分不同用途
const authStorage = localforage.createInstance({
  name: 'pacs-mobile',
  storeName: 'auth_data',
  description: '认证相关数据'
});

const uiStorage = localforage.createInstance({
  name: 'pacs-mobile',
  storeName: 'ui_state',
  description: 'UI状态和偏好设置'
});

// 兼容函数 - 从localStorage同步数据到localforage
const syncFromLocalStorage = async (key) => {
  try {
    // 检查localforage中是否已有数据
    const value = await authStorage.getItem(key);
    if (value === null) {
      // 从localStorage获取
      const localValue = localStorage.getItem(key);
      if (localValue !== null) {
        // 同步到localforage
        await authStorage.setItem(key, localValue);
        return localValue;
      }
    }
    return value;
  } catch (err) {
    console.error('同步数据失败:', err);
    return localStorage.getItem(key);
  }
};

export { 
  localforage, // 默认实例
  authStorage, // 认证数据存储
  uiStorage,   // UI状态存储
  syncFromLocalStorage // 数据同步工具
}; 