package com.ruoyi.common.utils.sms;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsResponseBody;
import com.aliyun.teaopenapi.models.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Map;

/**
 * 阿里云短信服务工具类
 *
 * <AUTHOR>
 */
@Component
public class AliSmsUtils {
    private static final Logger log = LoggerFactory.getLogger(AliSmsUtils.class);

    /**
     * 阿里云短信服务的accessKeyId
     */
    @Value("${aliyun.sms.accessKeyId:}")
    private String accessKeyId;

    /**
     * 阿里云短信服务的accessKeySecret
     */
    @Value("${aliyun.sms.accessKeySecret:}")
    private String accessKeySecret;

    /**
     * 短信签名
     */
    @Value("${aliyun.sms.signName:}")
    private String signName;

    /**
     * 地域ID，默认为cn-hangzhou
     */
    @Value("${aliyun.sms.regionId:cn-hangzhou}")
    private String regionId;

    /**
     * 初始化客户端
     *
     * @return 阿里云短信客户端
     * @throws Exception 初始化异常
     */
    private Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    /**
     * 发送短信
     *
     * @param phoneNumbers 手机号码（支持多个，以英文逗号分隔）
     * @param templateCode 短信模板CODE
     * @param templateParam 短信模板参数（JSON格式）
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String phoneNumbers, String templateCode, String templateParam) {
        try {
            // 初始化客户端
            Client client = createClient();

            // 创建请求对象
            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(phoneNumbers)
                    .setSignName(signName)
                    .setTemplateCode(templateCode);

            // 设置模板参数
            if (templateParam != null) {
                request.setTemplateParam(templateParam);
            }

            // 发送短信
            SendSmsResponse response = client.sendSms(request);
            SendSmsResponseBody body = response.getBody();

            log.info("短信发送结果 - 手机号: {}, 状态码: {}, 消息: {}, 请求ID: {}",
                    phoneNumbers, body.getCode(), body.getMessage(), body.getRequestId());

            return response;
        } catch (Exception e) {
            log.error("短信发送异常 - 手机号: {}, 错误信息: {}",
                    phoneNumbers, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 发送短信（使用Map传递模板参数）
     *
     * @param phoneNumbers 手机号码（支持多个，以英文逗号分隔）
     * @param templateCode 短信模板CODE
     * @param templateParamMap 短信模板参数Map
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String phoneNumbers, String templateCode, Map<String, String> templateParamMap) {
        // 将Map转换为JSON字符串
        StringBuilder templateParam = new StringBuilder("{");
        if (templateParamMap != null && !templateParamMap.isEmpty()) {
            int i = 0;
            for (Map.Entry<String, String> entry : templateParamMap.entrySet()) {
                if (i > 0) {
                    templateParam.append(",");
                }
                templateParam.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
                i++;
            }
        }
        templateParam.append("}");

        return sendSms(phoneNumbers, templateCode, templateParam.toString());
    }

    /**
     * 校验短信发送是否成功
     *
     * @param response 发送短信的响应结果
     * @return 是否成功
     */
    public boolean isSuccess(SendSmsResponse response) {
        return response != null && response.getBody() != null && "OK".equals(response.getBody().getCode());
    }

    /**
     * 生成六位数字验证码
     * 使用SecureRandom提高随机性，避免伪随机问题
     *
     * @return 六位数字验证码
     */
    public static String generateSixDigitCode() {
        // 使用SecureRandom代替Random，提供更高的随机性和安全性
        SecureRandom secureRandom = new SecureRandom();
        // 添加额外的熵来增强随机性
        secureRandom.setSeed(secureRandom.generateSeed(16));

        // 生成0-999999之间的随机数
        int code = secureRandom.nextInt(1000000);
        // 格式化为六位数，不足六位前面补0
        return String.format("%06d", code);
    }
}
